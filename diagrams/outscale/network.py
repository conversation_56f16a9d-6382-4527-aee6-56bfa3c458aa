# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Outscale


class _Network(_Outscale):
    _type = "network"
    _icon_dir = "resources/outscale/network"


class ClientVpn(_Network):
    _icon = "client-vpn.png"


class InternetService(_Network):
    _icon = "internet-service.png"


class LoadBalancer(_Network):
    _icon = "load-balancer.png"


class NatService(_Network):
    _icon = "nat-service.png"


class Net(_Network):
    _icon = "net.png"


class SiteToSiteVpng(_Network):
    _icon = "site-to-site-vpng.png"


# Aliases
