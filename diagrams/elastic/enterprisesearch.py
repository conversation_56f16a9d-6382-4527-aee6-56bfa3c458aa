# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Elastic


class _Enterprisesearch(_Elastic):
    _type = "enterprisesearch"
    _icon_dir = "resources/elastic/enterprisesearch"


class AppSearch(_Enterprisesearch):
    _icon = "app-search.png"


class Crawler(_Enterprisesearch):
    _icon = "crawler.png"


class EnterpriseSearch(_Enterprisesearch):
    _icon = "enterprise-search.png"


class SiteSearch(_Enterprisesearch):
    _icon = "site-search.png"


class WorkplaceSearch(_Enterprisesearch):
    _icon = "workplace-search.png"


# Aliases
