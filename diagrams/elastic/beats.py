# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Elastic


class _Beats(_Elastic):
    _type = "beats"
    _icon_dir = "resources/elastic/beats"


class APM(_Beats):
    _icon = "apm.png"


class Auditbeat(_Beats):
    _icon = "auditbeat.png"


class Filebeat(_Beats):
    _icon = "filebeat.png"


class Functionbeat(_Beats):
    _icon = "functionbeat.png"


class Heartbeat(_Beats):
    _icon = "heartbeat.png"


class Metricbeat(_Beats):
    _icon = "metricbeat.png"


class Packetbeat(_Beats):
    _icon = "packetbeat.png"


class Winlogbeat(_Beats):
    _icon = "winlogbeat.png"


# Aliases
