# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Elastic


class _Observability(_Elastic):
    _type = "observability"
    _icon_dir = "resources/elastic/observability"


class APM(_Observability):
    _icon = "apm.png"


class Logs(_Observability):
    _icon = "logs.png"


class Metrics(_Observability):
    _icon = "metrics.png"


class Observability(_Observability):
    _icon = "observability.png"


class Uptime(_Observability):
    _icon = "uptime.png"


# Aliases
