# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Elastic


class _Elasticsearch(_Elastic):
    _type = "elasticsearch"
    _icon_dir = "resources/elastic/elasticsearch"


class Alerting(_Elasticsearch):
    _icon = "alerting.png"


class Beats(_Elasticsearch):
    _icon = "beats.png"


class Elasticsearch(_Elasticsearch):
    _icon = "elasticsearch.png"


class Kibana(_Elasticsearch):
    _icon = "kibana.png"


class LogstashPipeline(_Elasticsearch):
    _icon = "logstash-pipeline.png"


class Logstash(_Elasticsearch):
    _icon = "logstash.png"


class MachineLearning(_Elasticsearch):
    _icon = "machine-learning.png"


class MapServices(_Elasticsearch):
    _icon = "map-services.png"


class Maps(_Elasticsearch):
    _icon = "maps.png"


class Monitoring(_Elasticsearch):
    _icon = "monitoring.png"


class SearchableSnapshots(_Elasticsearch):
    _icon = "searchable-snapshots.png"


class SecuritySettings(_Elasticsearch):
    _icon = "security-settings.png"


class SQL(_Elasticsearch):
    _icon = "sql.png"


class Stack(_Elasticsearch):
    _icon = "stack.png"


# Aliases

ElasticSearch = Elasticsearch
LogStash = Logstash
ML = MachineLearning
