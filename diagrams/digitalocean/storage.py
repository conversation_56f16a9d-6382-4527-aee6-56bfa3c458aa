# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _DigitalOcean


class _Storage(_DigitalOcean):
    _type = "storage"
    _icon_dir = "resources/digitalocean/storage"


class Folder(_Storage):
    _icon = "folder.png"


class Space(_Storage):
    _icon = "space.png"


class VolumeSnapshot(_Storage):
    _icon = "volume-snapshot.png"


class Volume(_Storage):
    _icon = "volume.png"


# Aliases
