# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _DigitalOcean


class _Database(_DigitalOcean):
    _type = "database"
    _icon_dir = "resources/digitalocean/database"


class DbaasPrimaryStandbyMore(_Database):
    _icon = "dbaas-primary-standby-more.png"


class DbaasPrimary(_Database):
    _icon = "dbaas-primary.png"


class DbaasReadOnly(_Database):
    _icon = "dbaas-read-only.png"


class DbaasStandby(_Database):
    _icon = "dbaas-standby.png"


# Aliases
