# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _DigitalOcean


class _Compute(_DigitalOcean):
    _type = "compute"
    _icon_dir = "resources/digitalocean/compute"


class Containers(_Compute):
    _icon = "containers.png"


class Docker(_Compute):
    _icon = "docker.png"


class DropletConnect(_Compute):
    _icon = "droplet-connect.png"


class DropletSnapshot(_Compute):
    _icon = "droplet-snapshot.png"


class Droplet(_Compute):
    _icon = "droplet.png"


class K8SCluster(_Compute):
    _icon = "k8s-cluster.png"


class K8SNodePool(_Compute):
    _icon = "k8s-node-pool.png"


class K8SNode(_Compute):
    _icon = "k8s-node.png"


# Aliases
