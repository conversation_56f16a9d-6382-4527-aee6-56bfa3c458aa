# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Azure


class _Compute(_Azure):
    _type = "compute"
    _icon_dir = "resources/azure/compute"


class AppServices(_Compute):
    _icon = "app-services.png"


class AutomanagedVM(_Compute):
    _icon = "automanaged-vm.png"


class AvailabilitySets(_Compute):
    _icon = "availability-sets.png"


class BatchAccounts(_Compute):
    _icon = "batch-accounts.png"


class CitrixVirtualDesktopsEssentials(_Compute):
    _icon = "citrix-virtual-desktops-essentials.png"


class CloudServicesClassic(_Compute):
    _icon = "cloud-services-classic.png"


class CloudServices(_Compute):
    _icon = "cloud-services.png"


class CloudsimpleVirtualMachines(_Compute):
    _icon = "cloudsimple-virtual-machines.png"


class ContainerApps(_Compute):
    _icon = "container-apps.png"


class ContainerInstances(_Compute):
    _icon = "container-instances.png"


class ContainerRegistries(_Compute):
    _icon = "container-registries.png"


class DiskEncryptionSets(_Compute):
    _icon = "disk-encryption-sets.png"


class DiskSnapshots(_Compute):
    _icon = "disk-snapshots.png"


class Disks(_Compute):
    _icon = "disks.png"


class FunctionApps(_Compute):
    _icon = "function-apps.png"


class ImageDefinitions(_Compute):
    _icon = "image-definitions.png"


class ImageVersions(_Compute):
    _icon = "image-versions.png"


class KubernetesServices(_Compute):
    _icon = "kubernetes-services.png"


class MeshApplications(_Compute):
    _icon = "mesh-applications.png"


class OsImages(_Compute):
    _icon = "os-images.png"


class SAPHANAOnAzure(_Compute):
    _icon = "sap-hana-on-azure.png"


class ServiceFabricClusters(_Compute):
    _icon = "service-fabric-clusters.png"


class SharedImageGalleries(_Compute):
    _icon = "shared-image-galleries.png"


class SpringCloud(_Compute):
    _icon = "spring-cloud.png"


class VMClassic(_Compute):
    _icon = "vm-classic.png"


class VMImages(_Compute):
    _icon = "vm-images.png"


class VMLinux(_Compute):
    _icon = "vm-linux.png"


class VMScaleSet(_Compute):
    _icon = "vm-scale-set.png"


class VMWindows(_Compute):
    _icon = "vm-windows.png"


class VM(_Compute):
    _icon = "vm.png"


class Workspaces(_Compute):
    _icon = "workspaces.png"


# Aliases

ACR = ContainerRegistries
AKS = KubernetesServices
VMSS = VMScaleSet
