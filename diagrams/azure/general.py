# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Azure


class _General(_Azure):
    _type = "general"
    _icon_dir = "resources/azure/general"


class Allresources(_General):
    _icon = "allresources.png"


class Azurehome(_General):
    _icon = "azurehome.png"


class Developertools(_General):
    _icon = "developertools.png"


class Helpsupport(_General):
    _icon = "helpsupport.png"


class Information(_General):
    _icon = "information.png"


class Managementgroups(_General):
    _icon = "managementgroups.png"


class Marketplace(_General):
    _icon = "marketplace.png"


class Quickstartcenter(_General):
    _icon = "quickstartcenter.png"


class Recent(_General):
    _icon = "recent.png"


class Reservations(_General):
    _icon = "reservations.png"


class Resource(_General):
    _icon = "resource.png"


class Resourcegroups(_General):
    _icon = "resourcegroups.png"


class Servicehealth(_General):
    _icon = "servicehealth.png"


class Shareddashboard(_General):
    _icon = "shareddashboard.png"


class Subscriptions(_General):
    _icon = "subscriptions.png"


class Support(_General):
    _icon = "support.png"


class Supportrequests(_General):
    _icon = "supportrequests.png"


class Tag(_General):
    _icon = "tag.png"


class Tags(_General):
    _icon = "tags.png"


class Templates(_General):
    _icon = "templates.png"


class Twousericon(_General):
    _icon = "twousericon.png"


class Userhealthicon(_General):
    _icon = "userhealthicon.png"


class Usericon(_General):
    _icon = "usericon.png"


class Userprivacy(_General):
    _icon = "userprivacy.png"


class Userresource(_General):
    _icon = "userresource.png"


class Whatsnew(_General):
    _icon = "whatsnew.png"


# Aliases
