# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Azure


class _Web(_Azure):
    _type = "web"
    _icon_dir = "resources/azure/web"


class APIConnections(_Web):
    _icon = "api-connections.png"


class AppServiceCertificates(_Web):
    _icon = "app-service-certificates.png"


class AppServiceDomains(_Web):
    _icon = "app-service-domains.png"


class AppServiceEnvironments(_Web):
    _icon = "app-service-environments.png"


class AppServicePlans(_Web):
    _icon = "app-service-plans.png"


class AppServices(_Web):
    _icon = "app-services.png"


class MediaServices(_Web):
    _icon = "media-services.png"


class NotificationHubNamespaces(_Web):
    _icon = "notification-hub-namespaces.png"


class Search(_Web):
    _icon = "search.png"


class Signalr(_Web):
    _icon = "signalr.png"


# Aliases
