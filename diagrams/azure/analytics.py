# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Azure


class _Analytics(_Azure):
    _type = "analytics"
    _icon_dir = "resources/azure/analytics"


class AnalysisServices(_Analytics):
    _icon = "analysis-services.png"


class DataExplorerClusters(_Analytics):
    _icon = "data-explorer-clusters.png"


class DataFactories(_Analytics):
    _icon = "data-factories.png"


class DataLakeAnalytics(_Analytics):
    _icon = "data-lake-analytics.png"


class DataLakeStoreGen1(_Analytics):
    _icon = "data-lake-store-gen1.png"


class Databricks(_Analytics):
    _icon = "databricks.png"


class EventHubClusters(_Analytics):
    _icon = "event-hub-clusters.png"


class EventHubs(_Analytics):
    _icon = "event-hubs.png"


class Hdinsightclusters(_Analytics):
    _icon = "hdinsightclusters.png"


class LogAnalyticsWorkspaces(_Analytics):
    _icon = "log-analytics-workspaces.png"


class StreamAnalyticsJobs(_Analytics):
    _icon = "stream-analytics-jobs.png"


class SynapseAnalytics(_Analytics):
    _icon = "synapse-analytics.png"


# Aliases
