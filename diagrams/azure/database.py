# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Azure


class _Database(_Azure):
    _type = "database"
    _icon_dir = "resources/azure/database"


class BlobStorage(_Database):
    _icon = "blob-storage.png"


class CacheForRedis(_Database):
    _icon = "cache-for-redis.png"


class CosmosDb(_Database):
    _icon = "cosmos-db.png"


class DataExplorerClusters(_Database):
    _icon = "data-explorer-clusters.png"


class DataFactory(_Database):
    _icon = "data-factory.png"


class DataLake(_Database):
    _icon = "data-lake.png"


class DatabaseForMariadbServers(_Database):
    _icon = "database-for-mariadb-servers.png"


class DatabaseForMysqlServers(_Database):
    _icon = "database-for-mysql-servers.png"


class DatabaseForPostgresqlServers(_Database):
    _icon = "database-for-postgresql-servers.png"


class ElasticDatabasePools(_Database):
    _icon = "elastic-database-pools.png"


class ElasticJobAgents(_Database):
    _icon = "elastic-job-agents.png"


class InstancePools(_Database):
    _icon = "instance-pools.png"


class ManagedDatabases(_Database):
    _icon = "managed-databases.png"


class SQLDatabases(_Database):
    _icon = "sql-databases.png"


class SQLDatawarehouse(_Database):
    _icon = "sql-datawarehouse.png"


class SQLManagedInstances(_Database):
    _icon = "sql-managed-instances.png"


class SQLServerStretchDatabases(_Database):
    _icon = "sql-server-stretch-databases.png"


class SQLServers(_Database):
    _icon = "sql-servers.png"


class SQLVM(_Database):
    _icon = "sql-vm.png"


class SQL(_Database):
    _icon = "sql.png"


class SsisLiftAndShiftIr(_Database):
    _icon = "ssis-lift-and-shift-ir.png"


class SynapseAnalytics(_Database):
    _icon = "synapse-analytics.png"


class VirtualClusters(_Database):
    _icon = "virtual-clusters.png"


class VirtualDatacenter(_Database):
    _icon = "virtual-datacenter.png"


# Aliases
