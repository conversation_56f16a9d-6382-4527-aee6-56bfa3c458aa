# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Programming


class _Framework(_Programming):
    _type = "framework"
    _icon_dir = "resources/programming/framework"


class Angular(_Framework):
    _icon = "angular.png"


class Backbone(_Framework):
    _icon = "backbone.png"


class Camel(_Framework):
    _icon = "camel.png"


class Django(_Framework):
    _icon = "django.png"


class Dotnet(_Framework):
    _icon = "dotnet.png"


class Ember(_Framework):
    _icon = "ember.png"


class Fastapi(_Framework):
    _icon = "fastapi.png"


class Flask(_Framework):
    _icon = "flask.png"


class Flutter(_Framework):
    _icon = "flutter.png"


class Graphql(_Framework):
    _icon = "graphql.png"


class Hibernate(_Framework):
    _icon = "hibernate.png"


class Jhipster(_Framework):
    _icon = "jhipster.png"


class Laravel(_Framework):
    _icon = "laravel.png"


class Micronaut(_Framework):
    _icon = "micronaut.png"


class Nextjs(_Framework):
    _icon = "nextjs.png"


class Phoenix(_Framework):
    _icon = "phoenix.png"


class Quarkus(_Framework):
    _icon = "quarkus.png"


class Rails(_Framework):
    _icon = "rails.png"


class React(_Framework):
    _icon = "react.png"


class Spring(_Framework):
    _icon = "spring.png"


class Sqlpage(_Framework):
    _icon = "sqlpage.png"


class Starlette(_Framework):
    _icon = "starlette.png"


class Svelte(_Framework):
    _icon = "svelte.png"


class Vercel(_Framework):
    _icon = "vercel.png"


class Vue(_Framework):
    _icon = "vue.png"


# Aliases

FastAPI = Fastapi
GraphQL = Graphql
DotNet = Dotnet
NextJs = Nextjs
