# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Programming


class _Language(_Programming):
    _type = "language"
    _icon_dir = "resources/programming/language"


class Bash(_Language):
    _icon = "bash.png"


class C(_Language):
    _icon = "c.png"


class Cpp(_Language):
    _icon = "cpp.png"


class Csharp(_Language):
    _icon = "csharp.png"


class Dart(_Language):
    _icon = "dart.png"


class Elixir(_Language):
    _icon = "elixir.png"


class Erlang(_Language):
    _icon = "erlang.png"


class Go(_Language):
    _icon = "go.png"


class Java(_Language):
    _icon = "java.png"


class Javascript(_Language):
    _icon = "javascript.png"


class Kotlin(_Language):
    _icon = "kotlin.png"


class Latex(_Language):
    _icon = "latex.png"


class Matlab(_Language):
    _icon = "matlab.png"


class Nodejs(_Language):
    _icon = "nodejs.png"


class Php(_Language):
    _icon = "php.png"


class Python(_Language):
    _icon = "python.png"


class R(_Language):
    _icon = "r.png"


class Ruby(_Language):
    _icon = "ruby.png"


class Rust(_Language):
    _icon = "rust.png"


class Scala(_Language):
    _icon = "scala.png"


class Sql(_Language):
    _icon = "sql.png"


class Swift(_Language):
    _icon = "swift.png"


class Typescript(_Language):
    _icon = "typescript.png"


# Aliases

JavaScript = Javascript
NodeJS = Nodejs
PHP = Php
TypeScript = Typescript
