# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Programming


class _Flowchart(_Programming):
    _type = "flowchart"
    _icon_dir = "resources/programming/flowchart"


class Action(_Flowchart):
    _icon = "action.png"


class Collate(_Flowchart):
    _icon = "collate.png"


class Database(_Flowchart):
    _icon = "database.png"


class Decision(_Flowchart):
    _icon = "decision.png"


class Delay(_Flowchart):
    _icon = "delay.png"


class Display(_Flowchart):
    _icon = "display.png"


class Document(_Flowchart):
    _icon = "document.png"


class InputOutput(_Flowchart):
    _icon = "input-output.png"


class Inspection(_Flowchart):
    _icon = "inspection.png"


class InternalStorage(_Flowchart):
    _icon = "internal-storage.png"


class LoopLimit(_Flowchart):
    _icon = "loop-limit.png"


class ManualInput(_Flowchart):
    _icon = "manual-input.png"


class ManualLoop(_Flowchart):
    _icon = "manual-loop.png"


class Merge(_Flowchart):
    _icon = "merge.png"


class MultipleDocuments(_Flowchart):
    _icon = "multiple-documents.png"


class OffPageConnectorLeft(_Flowchart):
    _icon = "off-page-connector-left.png"


class OffPageConnectorRight(_Flowchart):
    _icon = "off-page-connector-right.png"


class Or(_Flowchart):
    _icon = "or.png"


class PredefinedProcess(_Flowchart):
    _icon = "predefined-process.png"


class Preparation(_Flowchart):
    _icon = "preparation.png"


class Sort(_Flowchart):
    _icon = "sort.png"


class StartEnd(_Flowchart):
    _icon = "start-end.png"


class StoredData(_Flowchart):
    _icon = "stored-data.png"


class SummingJunction(_Flowchart):
    _icon = "summing-junction.png"


# Aliases
