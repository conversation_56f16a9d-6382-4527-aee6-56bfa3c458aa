# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _GIS


class _Server(_GIS):
    _type = "server"
    _icon_dir = "resources/gis/server"


class Actinia(_Server):
    _icon = "actinia.png"


class Baremaps(_Server):
    _icon = "baremaps.png"


class Deegree(_Server):
    _icon = "deegree.png"


class G3WSuite(_Server):
    _icon = "g3w-suite.png"


class Geohealthcheck(_Server):
    _icon = "geohealthcheck.png"


class Geomapfish(_Server):
    _icon = "geomapfish.png"


class Geomesa(_Server):
    _icon = "geomesa.png"


class Geonetwork(_Server):
    _icon = "geonetwork.png"


class Geonode(_Server):
    _icon = "geonode.png"


class Georchestra(_Server):
    _icon = "georchestra.png"


class Geoserver(_Server):
    _icon = "geoserver.png"


class Geowebcache(_Server):
    _icon = "geowebcache.png"


class Kepler(_Server):
    _icon = "kepler.png"


class Mapproxy(_Server):
    _icon = "mapproxy.png"


class Mapserver(_Server):
    _icon = "mapserver.png"


class Mapstore(_Server):
    _icon = "mapstore.png"


class Mviewer(_Server):
    _icon = "mviewer.png"


class Pg_Tileserv(_Server):
    _icon = "pg_tileserv.png"


class Pycsw(_Server):
    _icon = "pycsw.png"


class Pygeoapi(_Server):
    _icon = "pygeoapi.png"


class QGISServer(_Server):
    _icon = "qgis-server.png"


class Zooproject(_Server):
    _icon = "zooproject.png"


# Aliases
