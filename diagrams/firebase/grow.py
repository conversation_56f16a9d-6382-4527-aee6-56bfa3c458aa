# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Firebase


class _Grow(_Firebase):
    _type = "grow"
    _icon_dir = "resources/firebase/grow"


class ABTesting(_Grow):
    _icon = "ab-testing.png"


class AppIndexing(_Grow):
    _icon = "app-indexing.png"


class DynamicLinks(_Grow):
    _icon = "dynamic-links.png"


class InAppMessaging(_Grow):
    _icon = "in-app-messaging.png"


class Invites(_Grow):
    _icon = "invites.png"


class Messaging(_Grow):
    _icon = "messaging.png"


class Predictions(_Grow):
    _icon = "predictions.png"


class RemoteConfig(_Grow):
    _icon = "remote-config.png"


# Aliases

FCM = Messaging
