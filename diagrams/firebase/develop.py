# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Firebase


class _Develop(_Firebase):
    _type = "develop"
    _icon_dir = "resources/firebase/develop"


class Authentication(_Develop):
    _icon = "authentication.png"


class Firestore(_Develop):
    _icon = "firestore.png"


class Functions(_Develop):
    _icon = "functions.png"


class Hosting(_Develop):
    _icon = "hosting.png"


class MLKit(_Develop):
    _icon = "ml-kit.png"


class RealtimeDatabase(_Develop):
    _icon = "realtime-database.png"


class Storage(_Develop):
    _icon = "storage.png"


# Aliases
