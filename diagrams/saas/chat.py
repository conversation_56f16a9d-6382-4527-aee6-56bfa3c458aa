# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Saas


class _Chat(_Saas):
    _type = "chat"
    _icon_dir = "resources/saas/chat"


class Discord(_Chat):
    _icon = "discord.png"


class Line(_Chat):
    _icon = "line.png"


class Mattermost(_Chat):
    _icon = "mattermost.png"


class Messenger(_Chat):
    _icon = "messenger.png"


class RocketChat(_Chat):
    _icon = "rocket-chat.png"


class Slack(_Chat):
    _icon = "slack.png"


class Teams(_Chat):
    _icon = "teams.png"


class Telegram(_Chat):
    _icon = "telegram.png"


# Aliases
