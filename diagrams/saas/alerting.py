# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _Saas


class _Alerting(_Saas):
    _type = "alerting"
    _icon_dir = "resources/saas/alerting"


class Newrelic(_Alerting):
    _icon = "newrelic.png"


class Opsgenie(_Alerting):
    _icon = "opsgenie.png"


class Pagerduty(_Alerting):
    _icon = "pagerduty.png"


class Pushover(_Alerting):
    _icon = "pushover.png"


class Xmatters(_Alerting):
    _icon = "xmatters.png"


# Aliases
