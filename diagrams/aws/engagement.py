# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AWS


class _Engagement(_AWS):
    _type = "engagement"
    _icon_dir = "resources/aws/engagement"


class Connect(_Engagement):
    _icon = "connect.png"


class CustomerEngagement(_Engagement):
    _icon = "customer-engagement.png"


class Pinpoint(_Engagement):
    _icon = "pinpoint.png"


class SimpleEmailServiceSesEmail(_Engagement):
    _icon = "simple-email-service-ses-email.png"


class SimpleEmailServiceSes(_Engagement):
    _icon = "simple-email-service-ses.png"


# Aliases

SES = SimpleEmailServiceSes
