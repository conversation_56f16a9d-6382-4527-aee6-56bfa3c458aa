# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AWS


class _Storage(_AWS):
    _type = "storage"
    _icon_dir = "resources/aws/storage"


class Backup(_Storage):
    _icon = "backup.png"


class CloudendureDisasterRecovery(_Storage):
    _icon = "cloudendure-disaster-recovery.png"


class EFSInfrequentaccessPrimaryBg(_Storage):
    _icon = "efs-infrequentaccess-primary-bg.png"


class EFSStandardPrimaryBg(_Storage):
    _icon = "efs-standard-primary-bg.png"


class ElasticBlockStoreEBSSnapshot(_Storage):
    _icon = "elastic-block-store-ebs-snapshot.png"


class ElasticBlockStoreEBSVolume(_Storage):
    _icon = "elastic-block-store-ebs-volume.png"


class ElasticBlockStoreEBS(_Storage):
    _icon = "elastic-block-store-ebs.png"


class ElasticFileSystemEFSFileSystem(_Storage):
    _icon = "elastic-file-system-efs-file-system.png"


class ElasticFileSystemEFS(_Storage):
    _icon = "elastic-file-system-efs.png"


class FsxForLustre(_Storage):
    _icon = "fsx-for-lustre.png"


class FsxForWindowsFileServer(_Storage):
    _icon = "fsx-for-windows-file-server.png"


class Fsx(_Storage):
    _icon = "fsx.png"


class MultipleVolumesResource(_Storage):
    _icon = "multiple-volumes-resource.png"


class S3AccessPoints(_Storage):
    _icon = "s3-access-points.png"


class S3GlacierArchive(_Storage):
    _icon = "s3-glacier-archive.png"


class S3GlacierVault(_Storage):
    _icon = "s3-glacier-vault.png"


class S3Glacier(_Storage):
    _icon = "s3-glacier.png"


class S3ObjectLambdaAccessPoints(_Storage):
    _icon = "s3-object-lambda-access-points.png"


class SimpleStorageServiceS3BucketWithObjects(_Storage):
    _icon = "simple-storage-service-s3-bucket-with-objects.png"


class SimpleStorageServiceS3Bucket(_Storage):
    _icon = "simple-storage-service-s3-bucket.png"


class SimpleStorageServiceS3Object(_Storage):
    _icon = "simple-storage-service-s3-object.png"


class SimpleStorageServiceS3(_Storage):
    _icon = "simple-storage-service-s3.png"


class SnowFamilySnowballImportExport(_Storage):
    _icon = "snow-family-snowball-import-export.png"


class SnowballEdge(_Storage):
    _icon = "snowball-edge.png"


class Snowball(_Storage):
    _icon = "snowball.png"


class Snowmobile(_Storage):
    _icon = "snowmobile.png"


class StorageGatewayCachedVolume(_Storage):
    _icon = "storage-gateway-cached-volume.png"


class StorageGatewayNonCachedVolume(_Storage):
    _icon = "storage-gateway-non-cached-volume.png"


class StorageGatewayVirtualTapeLibrary(_Storage):
    _icon = "storage-gateway-virtual-tape-library.png"


class StorageGateway(_Storage):
    _icon = "storage-gateway.png"


class Storage(_Storage):
    _icon = "storage.png"


# Aliases

CDR = CloudendureDisasterRecovery
EBS = ElasticBlockStoreEBS
EFS = ElasticFileSystemEFS
FSx = Fsx
S3 = SimpleStorageServiceS3
