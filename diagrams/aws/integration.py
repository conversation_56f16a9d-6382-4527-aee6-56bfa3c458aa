# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AWS


class _Integration(_AWS):
    _type = "integration"
    _icon_dir = "resources/aws/integration"


class ApplicationIntegration(_Integration):
    _icon = "application-integration.png"


class Appsync(_Integration):
    _icon = "appsync.png"


class ConsoleMobileApplication(_Integration):
    _icon = "console-mobile-application.png"


class EventResource(_Integration):
    _icon = "event-resource.png"


class EventbridgeCustomEventBusResource(_Integration):
    _icon = "eventbridge-custom-event-bus-resource.png"


class EventbridgeDefaultEventBusResource(_Integration):
    _icon = "eventbridge-default-event-bus-resource.png"


class EventbridgeEvent(_Integration):
    _icon = "eventbridge-event.png"


class EventbridgePipes(_Integration):
    _icon = "eventbridge-pipes.png"


class EventbridgeRule(_Integration):
    _icon = "eventbridge-rule.png"


class EventbridgeSaasPartnerEventBusResource(_Integration):
    _icon = "eventbridge-saas-partner-event-bus-resource.png"


class EventbridgeScheduler(_Integration):
    _icon = "eventbridge-scheduler.png"


class EventbridgeSchema(_Integration):
    _icon = "eventbridge-schema.png"


class Eventbridge(_Integration):
    _icon = "eventbridge.png"


class ExpressWorkflows(_Integration):
    _icon = "express-workflows.png"


class MQ(_Integration):
    _icon = "mq.png"


class SimpleNotificationServiceSnsEmailNotification(_Integration):
    _icon = "simple-notification-service-sns-email-notification.png"


class SimpleNotificationServiceSnsHttpNotification(_Integration):
    _icon = "simple-notification-service-sns-http-notification.png"


class SimpleNotificationServiceSnsTopic(_Integration):
    _icon = "simple-notification-service-sns-topic.png"


class SimpleNotificationServiceSns(_Integration):
    _icon = "simple-notification-service-sns.png"


class SimpleQueueServiceSqsMessage(_Integration):
    _icon = "simple-queue-service-sqs-message.png"


class SimpleQueueServiceSqsQueue(_Integration):
    _icon = "simple-queue-service-sqs-queue.png"


class SimpleQueueServiceSqs(_Integration):
    _icon = "simple-queue-service-sqs.png"


class StepFunctions(_Integration):
    _icon = "step-functions.png"


# Aliases

SNS = SimpleNotificationServiceSns
SQS = SimpleQueueServiceSqs
SF = StepFunctions
