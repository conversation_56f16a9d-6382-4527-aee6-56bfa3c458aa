# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AWS


class _Cost(_AWS):
    _type = "cost"
    _icon_dir = "resources/aws/cost"


class Budgets(_Cost):
    _icon = "budgets.png"


class CostAndUsageReport(_Cost):
    _icon = "cost-and-usage-report.png"


class CostExplorer(_Cost):
    _icon = "cost-explorer.png"


class CostManagement(_Cost):
    _icon = "cost-management.png"


class ReservedInstanceReporting(_Cost):
    _icon = "reserved-instance-reporting.png"


class SavingsPlans(_Cost):
    _icon = "savings-plans.png"


# Aliases
