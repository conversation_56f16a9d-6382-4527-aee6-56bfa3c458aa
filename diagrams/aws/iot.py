# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AWS


class _Iot(_AWS):
    _type = "iot"
    _icon_dir = "resources/aws/iot"


class Freertos(_Iot):
    _icon = "freertos.png"


class InternetOfThings(_Iot):
    _icon = "internet-of-things.png"


class Iot1Click(_Iot):
    _icon = "iot-1-click.png"


class IotAction(_Iot):
    _icon = "iot-action.png"


class IotActuator(_Iot):
    _icon = "iot-actuator.png"


class IotAlexaEcho(_Iot):
    _icon = "iot-alexa-echo.png"


class IotAlexaEnabledDevice(_Iot):
    _icon = "iot-alexa-enabled-device.png"


class IotAlexaSkill(_Iot):
    _icon = "iot-alexa-skill.png"


class IotAlexaVoiceService(_Iot):
    _icon = "iot-alexa-voice-service.png"


class IotAnalyticsChannel(_Iot):
    _icon = "iot-analytics-channel.png"


class IotAnalyticsDataSet(_Iot):
    _icon = "iot-analytics-data-set.png"


class IotAnalyticsDataStore(_Iot):
    _icon = "iot-analytics-data-store.png"


class IotAnalyticsNotebook(_Iot):
    _icon = "iot-analytics-notebook.png"


class IotAnalyticsPipeline(_Iot):
    _icon = "iot-analytics-pipeline.png"


class IotAnalytics(_Iot):
    _icon = "iot-analytics.png"


class IotBank(_Iot):
    _icon = "iot-bank.png"


class IotBicycle(_Iot):
    _icon = "iot-bicycle.png"


class IotButton(_Iot):
    _icon = "iot-button.png"


class IotCamera(_Iot):
    _icon = "iot-camera.png"


class IotCar(_Iot):
    _icon = "iot-car.png"


class IotCart(_Iot):
    _icon = "iot-cart.png"


class IotCertificate(_Iot):
    _icon = "iot-certificate.png"


class IotCoffeePot(_Iot):
    _icon = "iot-coffee-pot.png"


class IotCore(_Iot):
    _icon = "iot-core.png"


class IotDesiredState(_Iot):
    _icon = "iot-desired-state.png"


class IotDeviceDefender(_Iot):
    _icon = "iot-device-defender.png"


class IotDeviceGateway(_Iot):
    _icon = "iot-device-gateway.png"


class IotDeviceManagement(_Iot):
    _icon = "iot-device-management.png"


class IotDoorLock(_Iot):
    _icon = "iot-door-lock.png"


class IotEvents(_Iot):
    _icon = "iot-events.png"


class IotFactory(_Iot):
    _icon = "iot-factory.png"


class IotFireTvStick(_Iot):
    _icon = "iot-fire-tv-stick.png"


class IotFireTv(_Iot):
    _icon = "iot-fire-tv.png"


class IotGeneric(_Iot):
    _icon = "iot-generic.png"


class IotGreengrassConnector(_Iot):
    _icon = "iot-greengrass-connector.png"


class IotGreengrass(_Iot):
    _icon = "iot-greengrass.png"


class IotHardwareBoard(_Iot):
    _icon = "iot-hardware-board.png"


class IotHouse(_Iot):
    _icon = "iot-house.png"


class IotHttp(_Iot):
    _icon = "iot-http.png"


class IotHttp2(_Iot):
    _icon = "iot-http2.png"


class IotJobs(_Iot):
    _icon = "iot-jobs.png"


class IotLambda(_Iot):
    _icon = "iot-lambda.png"


class IotLightbulb(_Iot):
    _icon = "iot-lightbulb.png"


class IotMedicalEmergency(_Iot):
    _icon = "iot-medical-emergency.png"


class IotMqtt(_Iot):
    _icon = "iot-mqtt.png"


class IotOverTheAirUpdate(_Iot):
    _icon = "iot-over-the-air-update.png"


class IotPolicyEmergency(_Iot):
    _icon = "iot-policy-emergency.png"


class IotPolicy(_Iot):
    _icon = "iot-policy.png"


class IotReportedState(_Iot):
    _icon = "iot-reported-state.png"


class IotRule(_Iot):
    _icon = "iot-rule.png"


class IotSensor(_Iot):
    _icon = "iot-sensor.png"


class IotServo(_Iot):
    _icon = "iot-servo.png"


class IotShadow(_Iot):
    _icon = "iot-shadow.png"


class IotSimulator(_Iot):
    _icon = "iot-simulator.png"


class IotSitewise(_Iot):
    _icon = "iot-sitewise.png"


class IotThermostat(_Iot):
    _icon = "iot-thermostat.png"


class IotThingsGraph(_Iot):
    _icon = "iot-things-graph.png"


class IotTopic(_Iot):
    _icon = "iot-topic.png"


class IotTravel(_Iot):
    _icon = "iot-travel.png"


class IotUtility(_Iot):
    _icon = "iot-utility.png"


class IotWindfarm(_Iot):
    _icon = "iot-windfarm.png"


# Aliases

FreeRTOS = Freertos
IotBoard = IotHardwareBoard
