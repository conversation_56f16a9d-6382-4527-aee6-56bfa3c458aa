# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AWS


class _Management(_AWS):
    _type = "management"
    _icon_dir = "resources/aws/management"


class AmazonDevopsGuru(_Management):
    _icon = "amazon-devops-guru.png"


class AmazonManagedGrafana(_Management):
    _icon = "amazon-managed-grafana.png"


class AmazonManagedPrometheus(_Management):
    _icon = "amazon-managed-prometheus.png"


class AmazonManagedWorkflowsApacheAirflow(_Management):
    _icon = "amazon-managed-workflows-apache-airflow.png"


class AutoScaling(_Management):
    _icon = "auto-scaling.png"


class Chatbot(_Management):
    _icon = "chatbot.png"


class CloudformationChangeSet(_Management):
    _icon = "cloudformation-change-set.png"


class CloudformationStack(_Management):
    _icon = "cloudformation-stack.png"


class CloudformationTemplate(_Management):
    _icon = "cloudformation-template.png"


class Cloudformation(_Management):
    _icon = "cloudformation.png"


class Cloudtrail(_Management):
    _icon = "cloudtrail.png"


class CloudwatchAlarm(_Management):
    _icon = "cloudwatch-alarm.png"


class CloudwatchEventEventBased(_Management):
    _icon = "cloudwatch-event-event-based.png"


class CloudwatchEventTimeBased(_Management):
    _icon = "cloudwatch-event-time-based.png"


class CloudwatchLogs(_Management):
    _icon = "cloudwatch-logs.png"


class CloudwatchRule(_Management):
    _icon = "cloudwatch-rule.png"


class Cloudwatch(_Management):
    _icon = "cloudwatch.png"


class Codeguru(_Management):
    _icon = "codeguru.png"


class CommandLineInterface(_Management):
    _icon = "command-line-interface.png"


class Config(_Management):
    _icon = "config.png"


class ControlTower(_Management):
    _icon = "control-tower.png"


class LicenseManager(_Management):
    _icon = "license-manager.png"


class ManagedServices(_Management):
    _icon = "managed-services.png"


class ManagementAndGovernance(_Management):
    _icon = "management-and-governance.png"


class ManagementConsole(_Management):
    _icon = "management-console.png"


class OpsworksApps(_Management):
    _icon = "opsworks-apps.png"


class OpsworksDeployments(_Management):
    _icon = "opsworks-deployments.png"


class OpsworksInstances(_Management):
    _icon = "opsworks-instances.png"


class OpsworksLayers(_Management):
    _icon = "opsworks-layers.png"


class OpsworksMonitoring(_Management):
    _icon = "opsworks-monitoring.png"


class OpsworksPermissions(_Management):
    _icon = "opsworks-permissions.png"


class OpsworksResources(_Management):
    _icon = "opsworks-resources.png"


class OpsworksStack(_Management):
    _icon = "opsworks-stack.png"


class Opsworks(_Management):
    _icon = "opsworks.png"


class OrganizationsAccount(_Management):
    _icon = "organizations-account.png"


class OrganizationsOrganizationalUnit(_Management):
    _icon = "organizations-organizational-unit.png"


class Organizations(_Management):
    _icon = "organizations.png"


class PersonalHealthDashboard(_Management):
    _icon = "personal-health-dashboard.png"


class Proton(_Management):
    _icon = "proton.png"


class ServiceCatalog(_Management):
    _icon = "service-catalog.png"


class SystemsManagerAppConfig(_Management):
    _icon = "systems-manager-app-config.png"


class SystemsManagerAutomation(_Management):
    _icon = "systems-manager-automation.png"


class SystemsManagerDocuments(_Management):
    _icon = "systems-manager-documents.png"


class SystemsManagerInventory(_Management):
    _icon = "systems-manager-inventory.png"


class SystemsManagerMaintenanceWindows(_Management):
    _icon = "systems-manager-maintenance-windows.png"


class SystemsManagerOpscenter(_Management):
    _icon = "systems-manager-opscenter.png"


class SystemsManagerParameterStore(_Management):
    _icon = "systems-manager-parameter-store.png"


class SystemsManagerPatchManager(_Management):
    _icon = "systems-manager-patch-manager.png"


class SystemsManagerRunCommand(_Management):
    _icon = "systems-manager-run-command.png"


class SystemsManagerStateManager(_Management):
    _icon = "systems-manager-state-manager.png"


class SystemsManager(_Management):
    _icon = "systems-manager.png"


class TrustedAdvisorChecklistCost(_Management):
    _icon = "trusted-advisor-checklist-cost.png"


class TrustedAdvisorChecklistFaultTolerant(_Management):
    _icon = "trusted-advisor-checklist-fault-tolerant.png"


class TrustedAdvisorChecklistPerformance(_Management):
    _icon = "trusted-advisor-checklist-performance.png"


class TrustedAdvisorChecklistSecurity(_Management):
    _icon = "trusted-advisor-checklist-security.png"


class TrustedAdvisorChecklist(_Management):
    _icon = "trusted-advisor-checklist.png"


class TrustedAdvisor(_Management):
    _icon = "trusted-advisor.png"


class UserNotifications(_Management):
    _icon = "user-notifications.png"


class WellArchitectedTool(_Management):
    _icon = "well-architected-tool.png"


# Aliases

SSM = SystemsManager
ParameterStore = SystemsManagerParameterStore
