# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AWS


class _Database(_AWS):
    _type = "database"
    _icon_dir = "resources/aws/database"


class AuroraInstance(_Database):
    _icon = "aurora-instance.png"


class Aurora(_Database):
    _icon = "aurora.png"


class DatabaseMigrationServiceDatabaseMigrationWorkflow(_Database):
    _icon = "database-migration-service-database-migration-workflow.png"


class DatabaseMigrationService(_Database):
    _icon = "database-migration-service.png"


class Database(_Database):
    _icon = "database.png"


class DocumentdbMongodbCompatibility(_Database):
    _icon = "documentdb-mongodb-compatibility.png"


class DynamodbAttribute(_Database):
    _icon = "dynamodb-attribute.png"


class DynamodbAttributes(_Database):
    _icon = "dynamodb-attributes.png"


class DynamodbDax(_Database):
    _icon = "dynamodb-dax.png"


class DynamodbGlobalSecondaryIndex(_Database):
    _icon = "dynamodb-global-secondary-index.png"


class DynamodbItem(_Database):
    _icon = "dynamodb-item.png"


class DynamodbItems(_Database):
    _icon = "dynamodb-items.png"


class DynamodbStreams(_Database):
    _icon = "dynamodb-streams.png"


class DynamodbTable(_Database):
    _icon = "dynamodb-table.png"


class Dynamodb(_Database):
    _icon = "dynamodb.png"


class ElasticacheCacheNode(_Database):
    _icon = "elasticache-cache-node.png"


class ElasticacheForMemcached(_Database):
    _icon = "elasticache-for-memcached.png"


class ElasticacheForRedis(_Database):
    _icon = "elasticache-for-redis.png"


class Elasticache(_Database):
    _icon = "elasticache.png"


class KeyspacesManagedApacheCassandraService(_Database):
    _icon = "keyspaces-managed-apache-cassandra-service.png"


class Neptune(_Database):
    _icon = "neptune.png"


class QuantumLedgerDatabaseQldb(_Database):
    _icon = "quantum-ledger-database-qldb.png"


class RDSInstance(_Database):
    _icon = "rds-instance.png"


class RDSMariadbInstance(_Database):
    _icon = "rds-mariadb-instance.png"


class RDSMysqlInstance(_Database):
    _icon = "rds-mysql-instance.png"


class RDSOnVmware(_Database):
    _icon = "rds-on-vmware.png"


class RDSOracleInstance(_Database):
    _icon = "rds-oracle-instance.png"


class RDSPostgresqlInstance(_Database):
    _icon = "rds-postgresql-instance.png"


class RDSSqlServerInstance(_Database):
    _icon = "rds-sql-server-instance.png"


class RDS(_Database):
    _icon = "rds.png"


class RedshiftDenseComputeNode(_Database):
    _icon = "redshift-dense-compute-node.png"


class RedshiftDenseStorageNode(_Database):
    _icon = "redshift-dense-storage-node.png"


class Redshift(_Database):
    _icon = "redshift.png"


class Timestream(_Database):
    _icon = "timestream.png"


# Aliases

DMS = DatabaseMigrationService
DocumentDB = DocumentdbMongodbCompatibility
DAX = DynamodbDax
DynamodbGSI = DynamodbGlobalSecondaryIndex
DB = Database
DDB = Dynamodb
ElastiCache = Elasticache
QLDB = QuantumLedgerDatabaseQldb
