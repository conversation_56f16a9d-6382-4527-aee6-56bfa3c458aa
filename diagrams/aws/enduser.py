# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AWS


class _Enduser(_AWS):
    _type = "enduser"
    _icon_dir = "resources/aws/enduser"


class Appstream20(_Enduser):
    _icon = "appstream-2-0.png"


class DesktopAndAppStreaming(_Enduser):
    _icon = "desktop-and-app-streaming.png"


class Workdocs(_Enduser):
    _icon = "workdocs.png"


class Worklink(_Enduser):
    _icon = "worklink.png"


class Workspaces(_Enduser):
    _icon = "workspaces.png"


# Aliases
