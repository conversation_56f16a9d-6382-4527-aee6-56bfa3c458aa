# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AWS


class _Robotics(_AWS):
    _type = "robotics"
    _icon_dir = "resources/aws/robotics"


class RobomakerCloudExtensionRos(_Robotics):
    _icon = "robomaker-cloud-extension-ros.png"


class RobomakerDevelopmentEnvironment(_Robotics):
    _icon = "robomaker-development-environment.png"


class RobomakerFleetManagement(_Robotics):
    _icon = "robomaker-fleet-management.png"


class RobomakerSimulator(_Robotics):
    _icon = "robomaker-simulator.png"


class Robomaker(_Robotics):
    _icon = "robomaker.png"


class Robotics(_Robotics):
    _icon = "robotics.png"


# Aliases
