# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AWS


class _Security(_AWS):
    _type = "security"
    _icon_dir = "resources/aws/security"


class AdConnector(_Security):
    _icon = "ad-connector.png"


class Artifact(_Security):
    _icon = "artifact.png"


class CertificateAuthority(_Security):
    _icon = "certificate-authority.png"


class CertificateManager(_Security):
    _icon = "certificate-manager.png"


class CloudDirectory(_Security):
    _icon = "cloud-directory.png"


class Cloudhsm(_Security):
    _icon = "cloudhsm.png"


class Cognito(_Security):
    _icon = "cognito.png"


class Detective(_Security):
    _icon = "detective.png"


class DirectoryService(_Security):
    _icon = "directory-service.png"


class FirewallManager(_Security):
    _icon = "firewall-manager.png"


class Guardduty(_Security):
    _icon = "guardduty.png"


class IdentityAndAccessManagementIamAccessAnalyzer(_Security):
    _icon = "identity-and-access-management-iam-access-analyzer.png"


class IdentityAndAccessManagementIamAddOn(_Security):
    _icon = "identity-and-access-management-iam-add-on.png"


class IdentityAndAccessManagementIamAWSStsAlternate(_Security):
    _icon = "identity-and-access-management-iam-aws-sts-alternate.png"


class IdentityAndAccessManagementIamAWSSts(_Security):
    _icon = "identity-and-access-management-iam-aws-sts.png"


class IdentityAndAccessManagementIamDataEncryptionKey(_Security):
    _icon = "identity-and-access-management-iam-data-encryption-key.png"


class IdentityAndAccessManagementIamEncryptedData(_Security):
    _icon = "identity-and-access-management-iam-encrypted-data.png"


class IdentityAndAccessManagementIamLongTermSecurityCredential(_Security):
    _icon = "identity-and-access-management-iam-long-term-security-credential.png"


class IdentityAndAccessManagementIamMfaToken(_Security):
    _icon = "identity-and-access-management-iam-mfa-token.png"


class IdentityAndAccessManagementIamPermissions(_Security):
    _icon = "identity-and-access-management-iam-permissions.png"


class IdentityAndAccessManagementIamRole(_Security):
    _icon = "identity-and-access-management-iam-role.png"


class IdentityAndAccessManagementIamTemporarySecurityCredential(_Security):
    _icon = "identity-and-access-management-iam-temporary-security-credential.png"


class IdentityAndAccessManagementIam(_Security):
    _icon = "identity-and-access-management-iam.png"


class InspectorAgent(_Security):
    _icon = "inspector-agent.png"


class Inspector(_Security):
    _icon = "inspector.png"


class KeyManagementService(_Security):
    _icon = "key-management-service.png"


class Macie(_Security):
    _icon = "macie.png"


class ManagedMicrosoftAd(_Security):
    _icon = "managed-microsoft-ad.png"


class ResourceAccessManager(_Security):
    _icon = "resource-access-manager.png"


class SecretsManager(_Security):
    _icon = "secrets-manager.png"


class SecurityHubFinding(_Security):
    _icon = "security-hub-finding.png"


class SecurityHub(_Security):
    _icon = "security-hub.png"


class SecurityIdentityAndCompliance(_Security):
    _icon = "security-identity-and-compliance.png"


class ShieldAdvanced(_Security):
    _icon = "shield-advanced.png"


class Shield(_Security):
    _icon = "shield.png"


class SimpleAd(_Security):
    _icon = "simple-ad.png"


class SingleSignOn(_Security):
    _icon = "single-sign-on.png"


class WAFFilteringRule(_Security):
    _icon = "waf-filtering-rule.png"


class WAF(_Security):
    _icon = "waf.png"


# Aliases

ACM = CertificateManager
CloudHSM = Cloudhsm
DS = DirectoryService
FMS = FirewallManager
IAMAccessAnalyzer = IdentityAndAccessManagementIamAccessAnalyzer
IAMAWSSts = IdentityAndAccessManagementIamAWSSts
IAMPermissions = IdentityAndAccessManagementIamPermissions
IAMRole = IdentityAndAccessManagementIamRole
IAM = IdentityAndAccessManagementIam
KMS = KeyManagementService
RAM = ResourceAccessManager
