# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AWS


class _Migration(_AWS):
    _type = "migration"
    _icon_dir = "resources/aws/migration"


class ApplicationDiscoveryService(_Migration):
    _icon = "application-discovery-service.png"


class CloudendureMigration(_Migration):
    _icon = "cloudendure-migration.png"


class DatabaseMigrationService(_Migration):
    _icon = "database-migration-service.png"


class DatasyncAgent(_Migration):
    _icon = "datasync-agent.png"


class Datasync(_Migration):
    _icon = "datasync.png"


class MigrationAndTransfer(_Migration):
    _icon = "migration-and-transfer.png"


class MigrationHub(_Migration):
    _icon = "migration-hub.png"


class ServerMigrationService(_Migration):
    _icon = "server-migration-service.png"


class SnowballEdge(_Migration):
    _icon = "snowball-edge.png"


class Snowball(_Migration):
    _icon = "snowball.png"


class Snowmobile(_Migration):
    _icon = "snowmobile.png"


class TransferForSftp(_Migration):
    _icon = "transfer-for-sftp.png"


# Aliases

ADS = ApplicationDiscoveryService
CEM = CloudendureMigration
DMS = DatabaseMigrationService
MAT = MigrationAndTransfer
SMS = ServerMigrationService
