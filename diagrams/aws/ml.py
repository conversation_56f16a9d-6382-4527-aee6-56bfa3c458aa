# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AWS


class _ML(_AWS):
    _type = "ml"
    _icon_dir = "resources/aws/ml"


class ApacheMxnetOnAWS(_ML):
    _icon = "apache-mxnet-on-aws.png"


class AugmentedAi(_ML):
    _icon = "augmented-ai.png"


class Bedrock(_ML):
    _icon = "bedrock.png"


class Comprehend(_ML):
    _icon = "comprehend.png"


class DeepLearningAmis(_ML):
    _icon = "deep-learning-amis.png"


class DeepLearningContainers(_ML):
    _icon = "deep-learning-containers.png"


class Deepcomposer(_ML):
    _icon = "deepcomposer.png"


class Deeplens(_ML):
    _icon = "deeplens.png"


class Deepracer(_ML):
    _icon = "deepracer.png"


class ElasticInference(_ML):
    _icon = "elastic-inference.png"


class Forecast(_ML):
    _icon = "forecast.png"


class FraudDetector(_ML):
    _icon = "fraud-detector.png"


class Kendra(_ML):
    _icon = "kendra.png"


class Lex(_ML):
    _icon = "lex.png"


class MachineLearning(_ML):
    _icon = "machine-learning.png"


class Personalize(_ML):
    _icon = "personalize.png"


class Polly(_ML):
    _icon = "polly.png"


class Q(_ML):
    _icon = "q.png"


class RekognitionImage(_ML):
    _icon = "rekognition-image.png"


class RekognitionVideo(_ML):
    _icon = "rekognition-video.png"


class Rekognition(_ML):
    _icon = "rekognition.png"


class SagemakerGroundTruth(_ML):
    _icon = "sagemaker-ground-truth.png"


class SagemakerModel(_ML):
    _icon = "sagemaker-model.png"


class SagemakerNotebook(_ML):
    _icon = "sagemaker-notebook.png"


class SagemakerTrainingJob(_ML):
    _icon = "sagemaker-training-job.png"


class Sagemaker(_ML):
    _icon = "sagemaker.png"


class TensorflowOnAWS(_ML):
    _icon = "tensorflow-on-aws.png"


class Textract(_ML):
    _icon = "textract.png"


class Transcribe(_ML):
    _icon = "transcribe.png"


class Translate(_ML):
    _icon = "translate.png"


# Aliases

DLC = DeepLearningContainers
