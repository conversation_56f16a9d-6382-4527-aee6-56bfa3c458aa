# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AWS


class _Mobile(_AWS):
    _type = "mobile"
    _icon_dir = "resources/aws/mobile"


class Amplify(_Mobile):
    _icon = "amplify.png"


class APIGatewayEndpoint(_Mobile):
    _icon = "api-gateway-endpoint.png"


class APIGateway(_Mobile):
    _icon = "api-gateway.png"


class Appsync(_Mobile):
    _icon = "appsync.png"


class DeviceFarm(_Mobile):
    _icon = "device-farm.png"


class Mobile(_Mobile):
    _icon = "mobile.png"


class Pinpoint(_Mobile):
    _icon = "pinpoint.png"


# Aliases
