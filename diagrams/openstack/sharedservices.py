# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OpenStack


class _Sharedservices(_OpenStack):
    _type = "sharedservices"
    _icon_dir = "resources/openstack/sharedservices"


class Barbican(_Sharedservices):
    _icon = "barbican.png"


class Glance(_Sharedservices):
    _icon = "glance.png"


class Karbor(_Sharedservices):
    _icon = "karbor.png"


class Keystone(_Sharedservices):
    _icon = "keystone.png"


class Searchlight(_Sharedservices):
    _icon = "searchlight.png"


# Aliases
