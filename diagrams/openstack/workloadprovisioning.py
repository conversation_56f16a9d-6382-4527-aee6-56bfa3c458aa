# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OpenStack


class _Workloadprovisioning(_OpenStack):
    _type = "workloadprovisioning"
    _icon_dir = "resources/openstack/workloadprovisioning"


class Magnum(_Workloadprovisioning):
    _icon = "magnum.png"


class Sahara(_Workloadprovisioning):
    _icon = "sahara.png"


class Trove(_Workloadprovisioning):
    _icon = "trove.png"


# Aliases
