# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OpenStack


class _Applicationlifecycle(_OpenStack):
    _type = "applicationlifecycle"
    _icon_dir = "resources/openstack/applicationlifecycle"


class Freezer(_Applicationlifecycle):
    _icon = "freezer.png"


class Masakari(_Applicationlifecycle):
    _icon = "masakari.png"


class Murano(_Applicationlifecycle):
    _icon = "murano.png"


class Solum(_Applicationlifecycle):
    _icon = "solum.png"


# Aliases
