# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OpenStack


class _Deployment(_OpenStack):
    _type = "deployment"
    _icon_dir = "resources/openstack/deployment"


class Ansible(_Deployment):
    _icon = "ansible.png"


class Charms(_Deployment):
    _icon = "charms.png"


class Chef(_Deployment):
    _icon = "chef.png"


class Helm(_Deployment):
    _icon = "helm.png"


class Kolla(_Deployment):
    _icon = "kolla.png"


class Tripleo(_Deployment):
    _icon = "tripleo.png"


# Aliases

KollaAnsible = Kolla
TripleO = Tripleo
