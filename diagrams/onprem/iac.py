# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OnPrem


class _Iac(_OnPrem):
    _type = "iac"
    _icon_dir = "resources/onprem/iac"


class Ansible(_Iac):
    _icon = "ansible.png"


class Atlantis(_Iac):
    _icon = "atlantis.png"


class Awx(_Iac):
    _icon = "awx.png"


class Pulumi(_Iac):
    _icon = "pulumi.png"


class Puppet(_Iac):
    _icon = "puppet.png"


class Terraform(_Iac):
    _icon = "terraform.png"


# Aliases
