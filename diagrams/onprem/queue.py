# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OnPrem


class _Queue(_OnPrem):
    _type = "queue"
    _icon_dir = "resources/onprem/queue"


class Activemq(_Queue):
    _icon = "activemq.png"


class Celery(_Queue):
    _icon = "celery.png"


class Emqx(_Queue):
    _icon = "emqx.png"


class Kafka(_Queue):
    _icon = "kafka.png"


class Nats(_Queue):
    _icon = "nats.png"


class Rabbitmq(_Queue):
    _icon = "rabbitmq.png"


class Zeromq(_Queue):
    _icon = "zeromq.png"


# Aliases

ActiveMQ = Activemq
EMQX = Emqx
RabbitMQ = Rabbitmq
ZeroMQ = Zeromq
