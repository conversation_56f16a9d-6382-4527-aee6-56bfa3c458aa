# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OnPrem


class _Monitoring(_OnPrem):
    _type = "monitoring"
    _icon_dir = "resources/onprem/monitoring"


class Cortex(_Monitoring):
    _icon = "cortex.png"


class Datadog(_Monitoring):
    _icon = "datadog.png"


class Dynatrace(_Monitoring):
    _icon = "dynatrace.png"


class Grafana(_Monitoring):
    _icon = "grafana.png"


class Humio(_Monitoring):
    _icon = "humio.png"


class Mimir(_Monitoring):
    _icon = "mimir.png"


class Nagios(_Monitoring):
    _icon = "nagios.png"


class Newrelic(_Monitoring):
    _icon = "newrelic.png"


class PrometheusOperator(_Monitoring):
    _icon = "prometheus-operator.png"


class Prometheus(_Monitoring):
    _icon = "prometheus.png"


class Sentry(_Monitoring):
    _icon = "sentry.png"


class Splunk(_Monitoring):
    _icon = "splunk.png"


class Thanos(_Monitoring):
    _icon = "thanos.png"


class Zabbix(_Monitoring):
    _icon = "zabbix.png"


# Aliases
