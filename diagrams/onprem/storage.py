# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OnPrem


class _Storage(_OnPrem):
    _type = "storage"
    _icon_dir = "resources/onprem/storage"


class CephOsd(_Storage):
    _icon = "ceph-osd.png"


class Ceph(_Storage):
    _icon = "ceph.png"


class Glusterfs(_Storage):
    _icon = "glusterfs.png"


class Portworx(_Storage):
    _icon = "portworx.png"


# Aliases

CEPH = Ceph
CEPH_OSD = CephOsd
