# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OnPrem


class _Inmemory(_OnPrem):
    _type = "inmemory"
    _icon_dir = "resources/onprem/inmemory"


class Aerospike(_Inmemory):
    _icon = "aerospike.png"


class Hazelcast(_Inmemory):
    _icon = "hazelcast.png"


class Memcached(_Inmemory):
    _icon = "memcached.png"


class Redis(_Inmemory):
    _icon = "redis.png"


# Aliases
