# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AlibabaCloud


class _Security(_AlibabaCloud):
    _type = "security"
    _icon_dir = "resources/alibabacloud/security"


class AntiBotService(_Security):
    _icon = "anti-bot-service.png"


class AntiDdosBasic(_Security):
    _icon = "anti-ddos-basic.png"


class AntiDdosPro(_Security):
    _icon = "anti-ddos-pro.png"


class AntifraudService(_Security):
    _icon = "antifraud-service.png"


class BastionHost(_Security):
    _icon = "bastion-host.png"


class CloudFirewall(_Security):
    _icon = "cloud-firewall.png"


class CloudSecurityScanner(_Security):
    _icon = "cloud-security-scanner.png"


class ContentModeration(_Security):
    _icon = "content-moderation.png"


class CrowdsourcedSecurityTesting(_Security):
    _icon = "crowdsourced-security-testing.png"


class DataEncryptionService(_Security):
    _icon = "data-encryption-service.png"


class DbAudit(_Security):
    _icon = "db-audit.png"


class GameShield(_Security):
    _icon = "game-shield.png"


class IdVerification(_Security):
    _icon = "id-verification.png"


class ManagedSecurityService(_Security):
    _icon = "managed-security-service.png"


class SecurityCenter(_Security):
    _icon = "security-center.png"


class ServerGuard(_Security):
    _icon = "server-guard.png"


class SslCertificates(_Security):
    _icon = "ssl-certificates.png"


class WebApplicationFirewall(_Security):
    _icon = "web-application-firewall.png"


# Aliases

ABS = AntiBotService
AS = AntifraudService
CFW = CloudFirewall
CM = ContentModeration
DES = DataEncryptionService
WAF = WebApplicationFirewall
