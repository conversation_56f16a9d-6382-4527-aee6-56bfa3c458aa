# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _AlibabaCloud


class _Network(_AlibabaCloud):
    _type = "network"
    _icon_dir = "resources/alibabacloud/network"


class Cdn(_Network):
    _icon = "cdn.png"


class CloudEnterpriseNetwork(_Network):
    _icon = "cloud-enterprise-network.png"


class ElasticIpAddress(_Network):
    _icon = "elastic-ip-address.png"


class ExpressConnect(_Network):
    _icon = "express-connect.png"


class NatGateway(_Network):
    _icon = "nat-gateway.png"


class ServerLoadBalancer(_Network):
    _icon = "server-load-balancer.png"


class SmartAccessGateway(_Network):
    _icon = "smart-access-gateway.png"


class VirtualPrivateCloud(_Network):
    _icon = "virtual-private-cloud.png"


class VpnGateway(_Network):
    _icon = "vpn-gateway.png"


# Aliases

CEN = CloudEnterpriseNetwork
EIP = ElasticIpAddress
SLB = ServerLoadBalancer
VPC = VirtualPrivateCloud
