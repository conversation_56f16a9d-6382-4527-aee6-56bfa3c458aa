# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _GCP


class _Security(_GCP):
    _type = "security"
    _icon_dir = "resources/gcp/security"


class Iam(_Security):
    _icon = "iam.png"


class IAP(_Security):
    _icon = "iap.png"


class KeyManagementService(_Security):
    _icon = "key-management-service.png"


class ResourceManager(_Security):
    _icon = "resource-manager.png"


class SecurityCommandCenter(_Security):
    _icon = "security-command-center.png"


class SecurityScanner(_Security):
    _icon = "security-scanner.png"


# Aliases

KMS = KeyManagementService
SCC = SecurityCommandCenter
