# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _GCP


class _ML(_GCP):
    _type = "ml"
    _icon_dir = "resources/gcp/ml"


class AdvancedSolutionsLab(_ML):
    _icon = "advanced-solutions-lab.png"


class AIHub(_ML):
    _icon = "ai-hub.png"


class AIPlatformDataLabelingService(_ML):
    _icon = "ai-platform-data-labeling-service.png"


class AIPlatform(_ML):
    _icon = "ai-platform.png"


class AutomlNaturalLanguage(_ML):
    _icon = "automl-natural-language.png"


class AutomlTables(_ML):
    _icon = "automl-tables.png"


class AutomlTranslation(_ML):
    _icon = "automl-translation.png"


class AutomlVideoIntelligence(_ML):
    _icon = "automl-video-intelligence.png"


class AutomlVision(_ML):
    _icon = "automl-vision.png"


class Automl(_ML):
    _icon = "automl.png"


class DialogFlowEnterpriseEdition(_ML):
    _icon = "dialog-flow-enterprise-edition.png"


class InferenceAPI(_ML):
    _icon = "inference-api.png"


class JobsAPI(_ML):
    _icon = "jobs-api.png"


class NaturalLanguageAPI(_ML):
    _icon = "natural-language-api.png"


class RecommendationsAI(_ML):
    _icon = "recommendations-ai.png"


class SpeechToText(_ML):
    _icon = "speech-to-text.png"


class TextToSpeech(_ML):
    _icon = "text-to-speech.png"


class TPU(_ML):
    _icon = "tpu.png"


class TranslationAPI(_ML):
    _icon = "translation-api.png"


class VideoIntelligenceAPI(_ML):
    _icon = "video-intelligence-api.png"


class VisionAPI(_ML):
    _icon = "vision-api.png"


# Aliases

AutoML = Automl
NLAPI = NaturalLanguageAPI
STT = SpeechToText
TTS = TextToSpeech
