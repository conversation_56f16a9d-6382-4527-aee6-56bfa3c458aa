# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _GCP


class _Database(_GCP):
    _type = "database"
    _icon_dir = "resources/gcp/database"


class Bigtable(_Database):
    _icon = "bigtable.png"


class Datastore(_Database):
    _icon = "datastore.png"


class Firestore(_Database):
    _icon = "firestore.png"


class Memorystore(_Database):
    _icon = "memorystore.png"


class Spanner(_Database):
    _icon = "spanner.png"


class SQL(_Database):
    _icon = "sql.png"


# Aliases

BigTable = Bigtable
