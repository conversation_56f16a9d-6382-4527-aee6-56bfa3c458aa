# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _GCP


class _Analytics(_GCP):
    _type = "analytics"
    _icon_dir = "resources/gcp/analytics"


class Bigquery(_Analytics):
    _icon = "bigquery.png"


class Composer(_Analytics):
    _icon = "composer.png"


class DataCatalog(_Analytics):
    _icon = "data-catalog.png"


class DataFusion(_Analytics):
    _icon = "data-fusion.png"


class Dataflow(_Analytics):
    _icon = "dataflow.png"


class Datalab(_Analytics):
    _icon = "datalab.png"


class Dataprep(_Analytics):
    _icon = "dataprep.png"


class Dataproc(_Analytics):
    _icon = "dataproc.png"


class Genomics(_Analytics):
    _icon = "genomics.png"


class Pubsub(_Analytics):
    _icon = "pubsub.png"


# Aliases

BigQuery = Bigquery
PubSub = Pubsub
