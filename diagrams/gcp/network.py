# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _GCP


class _Network(_GCP):
    _type = "network"
    _icon_dir = "resources/gcp/network"


class Armor(_Network):
    _icon = "armor.png"


class CDN(_Network):
    _icon = "cdn.png"


class DedicatedInterconnect(_Network):
    _icon = "dedicated-interconnect.png"


class DNS(_Network):
    _icon = "dns.png"


class ExternalIpAddresses(_Network):
    _icon = "external-ip-addresses.png"


class FirewallRules(_Network):
    _icon = "firewall-rules.png"


class LoadBalancing(_Network):
    _icon = "load-balancing.png"


class NAT(_Network):
    _icon = "nat.png"


class Network(_Network):
    _icon = "network.png"


class PartnerInterconnect(_Network):
    _icon = "partner-interconnect.png"


class PremiumNetworkTier(_Network):
    _icon = "premium-network-tier.png"


class Router(_Network):
    _icon = "router.png"


class Routes(_Network):
    _icon = "routes.png"


class StandardNetworkTier(_Network):
    _icon = "standard-network-tier.png"


class TrafficDirector(_Network):
    _icon = "traffic-director.png"


class VirtualPrivateCloud(_Network):
    _icon = "virtual-private-cloud.png"


class VPN(_Network):
    _icon = "vpn.png"


# Aliases

VPC = VirtualPrivateCloud
