# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _GCP


class _Devtools(_GCP):
    _type = "devtools"
    _icon_dir = "resources/gcp/devtools"


class Build(_Devtools):
    _icon = "build.png"


class CodeForIntellij(_Devtools):
    _icon = "code-for-intellij.png"


class Code(_Devtools):
    _icon = "code.png"


class ContainerRegistry(_Devtools):
    _icon = "container-registry.png"


class GradleAppEnginePlugin(_Devtools):
    _icon = "gradle-app-engine-plugin.png"


class IdePlugins(_Devtools):
    _icon = "ide-plugins.png"


class MavenAppEnginePlugin(_Devtools):
    _icon = "maven-app-engine-plugin.png"


class Scheduler(_Devtools):
    _icon = "scheduler.png"


class SDK(_Devtools):
    _icon = "sdk.png"


class SourceRepositories(_Devtools):
    _icon = "source-repositories.png"


class Tasks(_Devtools):
    _icon = "tasks.png"


class TestLab(_Devtools):
    _icon = "test-lab.png"


class ToolsForEclipse(_Devtools):
    _icon = "tools-for-eclipse.png"


class ToolsForPowershell(_Devtools):
    _icon = "tools-for-powershell.png"


class ToolsForVisualStudio(_Devtools):
    _icon = "tools-for-visual-studio.png"


# Aliases

GCR = ContainerRegistry
