# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _IBM


class _Applications(_IBM):
    _type = "applications"
    _icon_dir = "resources/ibm/applications"


class ActionableInsight(_Applications):
    _icon = "actionable-insight.png"


class Annotate(_Applications):
    _icon = "annotate.png"


class ApiDeveloperPortal(_Applications):
    _icon = "api-developer-portal.png"


class ApiPolyglotRuntimes(_Applications):
    _icon = "api-polyglot-runtimes.png"


class AppServer(_Applications):
    _icon = "app-server.png"


class ApplicationLogic(_Applications):
    _icon = "application-logic.png"


class EnterpriseApplications(_Applications):
    _icon = "enterprise-applications.png"


class Index(_Applications):
    _icon = "index.png"


class IotApplication(_Applications):
    _icon = "iot-application.png"


class Microservice(_Applications):
    _icon = "microservice.png"


class MobileApp(_Applications):
    _icon = "mobile-app.png"


class Ontology(_Applications):
    _icon = "ontology.png"


class OpenSourceTools(_Applications):
    _icon = "open-source-tools.png"


class RuntimeServices(_Applications):
    _icon = "runtime-services.png"


class SaasApplications(_Applications):
    _icon = "saas-applications.png"


class ServiceBroker(_Applications):
    _icon = "service-broker.png"


class SpeechToText(_Applications):
    _icon = "speech-to-text.png"


class VisualRecognition(_Applications):
    _icon = "visual-recognition.png"


class Visualization(_Applications):
    _icon = "visualization.png"


# Aliases
