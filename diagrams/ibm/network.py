# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _IBM


class _Network(_IBM):
    _type = "network"
    _icon_dir = "resources/ibm/network"


class Bridge(_Network):
    _icon = "bridge.png"


class DirectLink(_Network):
    _icon = "direct-link.png"


class Enterprise(_Network):
    _icon = "enterprise.png"


class Firewall(_Network):
    _icon = "firewall.png"


class FloatingIp(_Network):
    _icon = "floating-ip.png"


class Gateway(_Network):
    _icon = "gateway.png"


class InternetServices(_Network):
    _icon = "internet-services.png"


class LoadBalancerListener(_Network):
    _icon = "load-balancer-listener.png"


class LoadBalancerPool(_Network):
    _icon = "load-balancer-pool.png"


class LoadBalancer(_Network):
    _icon = "load-balancer.png"


class LoadBalancingRouting(_Network):
    _icon = "load-balancing-routing.png"


class PublicGateway(_Network):
    _icon = "public-gateway.png"


class Region(_Network):
    _icon = "region.png"


class Router(_Network):
    _icon = "router.png"


class Rules(_Network):
    _icon = "rules.png"


class Subnet(_Network):
    _icon = "subnet.png"


class TransitGateway(_Network):
    _icon = "transit-gateway.png"


class Vpc(_Network):
    _icon = "vpc.png"


class VpnConnection(_Network):
    _icon = "vpn-connection.png"


class VpnGateway(_Network):
    _icon = "vpn-gateway.png"


class VpnPolicy(_Network):
    _icon = "vpn-policy.png"


# Aliases
