# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _IBM


class _Data(_IBM):
    _type = "data"
    _icon_dir = "resources/ibm/data"


class Caches(_Data):
    _icon = "caches.png"


class Cloud(_Data):
    _icon = "cloud.png"


class ConversationTrainedDeployed(_Data):
    _icon = "conversation-trained-deployed.png"


class DataServices(_Data):
    _icon = "data-services.png"


class DataSources(_Data):
    _icon = "data-sources.png"


class DeviceIdentityService(_Data):
    _icon = "device-identity-service.png"


class DeviceRegistry(_Data):
    _icon = "device-registry.png"


class EnterpriseData(_Data):
    _icon = "enterprise-data.png"


class EnterpriseUserDirectory(_Data):
    _icon = "enterprise-user-directory.png"


class FileRepository(_Data):
    _icon = "file-repository.png"


class GroundTruth(_Data):
    _icon = "ground-truth.png"


class Model(_Data):
    _icon = "model.png"


class TmsDataInterface(_Data):
    _icon = "tms-data-interface.png"


# Aliases
