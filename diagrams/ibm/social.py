# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _IBM


class _Social(_IBM):
    _type = "social"
    _icon_dir = "resources/ibm/social"


class Communities(_Social):
    _icon = "communities.png"


class FileSync(_Social):
    _icon = "file-sync.png"


class LiveCollaboration(_Social):
    _icon = "live-collaboration.png"


class Messaging(_Social):
    _icon = "messaging.png"


class Networking(_Social):
    _icon = "networking.png"


# Aliases
