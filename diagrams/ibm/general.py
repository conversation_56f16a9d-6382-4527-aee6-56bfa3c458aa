# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _IBM


class _General(_IBM):
    _type = "general"
    _icon_dir = "resources/ibm/general"


class CloudMessaging(_General):
    _icon = "cloud-messaging.png"


class CloudServices(_General):
    _icon = "cloud-services.png"


class Cloudant(_General):
    _icon = "cloudant.png"


class CognitiveServices(_General):
    _icon = "cognitive-services.png"


class DataSecurity(_General):
    _icon = "data-security.png"


class Enterprise(_General):
    _icon = "enterprise.png"


class GovernanceRiskCompliance(_General):
    _icon = "governance-risk-compliance.png"


class IBMContainers(_General):
    _icon = "ibm-containers.png"


class IBMPublicCloud(_General):
    _icon = "ibm-public-cloud.png"


class IdentityAccessManagement(_General):
    _icon = "identity-access-management.png"


class IdentityProvider(_General):
    _icon = "identity-provider.png"


class InfrastructureSecurity(_General):
    _icon = "infrastructure-security.png"


class Internet(_General):
    _icon = "internet.png"


class IotCloud(_General):
    _icon = "iot-cloud.png"


class MicroservicesApplication(_General):
    _icon = "microservices-application.png"


class MicroservicesMesh(_General):
    _icon = "microservices-mesh.png"


class MonitoringLogging(_General):
    _icon = "monitoring-logging.png"


class Monitoring(_General):
    _icon = "monitoring.png"


class ObjectStorage(_General):
    _icon = "object-storage.png"


class OfflineCapabilities(_General):
    _icon = "offline-capabilities.png"


class Openwhisk(_General):
    _icon = "openwhisk.png"


class PeerCloud(_General):
    _icon = "peer-cloud.png"


class RetrieveRank(_General):
    _icon = "retrieve-rank.png"


class Scalable(_General):
    _icon = "scalable.png"


class ServiceDiscoveryConfiguration(_General):
    _icon = "service-discovery-configuration.png"


class TextToSpeech(_General):
    _icon = "text-to-speech.png"


class TransformationConnectivity(_General):
    _icon = "transformation-connectivity.png"


# Aliases
