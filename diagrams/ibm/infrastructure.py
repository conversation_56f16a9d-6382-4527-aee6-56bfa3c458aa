# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _IBM


class _Infrastructure(_IBM):
    _type = "infrastructure"
    _icon_dir = "resources/ibm/infrastructure"


class Channels(_Infrastructure):
    _icon = "channels.png"


class CloudMessaging(_Infrastructure):
    _icon = "cloud-messaging.png"


class Dashboard(_Infrastructure):
    _icon = "dashboard.png"


class Diagnostics(_Infrastructure):
    _icon = "diagnostics.png"


class EdgeServices(_Infrastructure):
    _icon = "edge-services.png"


class EnterpriseMessaging(_Infrastructure):
    _icon = "enterprise-messaging.png"


class EventFeed(_Infrastructure):
    _icon = "event-feed.png"


class InfrastructureServices(_Infrastructure):
    _icon = "infrastructure-services.png"


class InterserviceCommunication(_Infrastructure):
    _icon = "interservice-communication.png"


class LoadBalancingRouting(_Infrastructure):
    _icon = "load-balancing-routing.png"


class MicroservicesMesh(_Infrastructure):
    _icon = "microservices-mesh.png"


class MobileBackend(_Infrastructure):
    _icon = "mobile-backend.png"


class MobileProviderNetwork(_Infrastructure):
    _icon = "mobile-provider-network.png"


class MonitoringLogging(_Infrastructure):
    _icon = "monitoring-logging.png"


class Monitoring(_Infrastructure):
    _icon = "monitoring.png"


class PeerServices(_Infrastructure):
    _icon = "peer-services.png"


class ServiceDiscoveryConfiguration(_Infrastructure):
    _icon = "service-discovery-configuration.png"


class TransformationConnectivity(_Infrastructure):
    _icon = "transformation-connectivity.png"


# Aliases
