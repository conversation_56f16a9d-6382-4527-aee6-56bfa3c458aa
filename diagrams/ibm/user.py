# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _IBM


class _User(_IBM):
    _type = "user"
    _icon_dir = "resources/ibm/user"


class Browser(_User):
    _icon = "browser.png"


class Device(_User):
    _icon = "device.png"


class IntegratedDigitalExperiences(_User):
    _icon = "integrated-digital-experiences.png"


class PhysicalEntity(_User):
    _icon = "physical-entity.png"


class Sensor(_User):
    _icon = "sensor.png"


class User(_User):
    _icon = "user.png"


# Aliases
