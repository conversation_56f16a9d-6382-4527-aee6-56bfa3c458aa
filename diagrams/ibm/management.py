# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _IBM


class _Management(_IBM):
    _type = "management"
    _icon_dir = "resources/ibm/management"


class AlertNotification(_Management):
    _icon = "alert-notification.png"


class ApiManagement(_Management):
    _icon = "api-management.png"


class CloudManagement(_Management):
    _icon = "cloud-management.png"


class ClusterManagement(_Management):
    _icon = "cluster-management.png"


class ContentManagement(_Management):
    _icon = "content-management.png"


class DataServices(_Management):
    _icon = "data-services.png"


class DeviceManagement(_Management):
    _icon = "device-management.png"


class InformationGovernance(_Management):
    _icon = "information-governance.png"


class ItServiceManagement(_Management):
    _icon = "it-service-management.png"


class Management(_Management):
    _icon = "management.png"


class MonitoringMetrics(_Management):
    _icon = "monitoring-metrics.png"


class ProcessManagement(_Management):
    _icon = "process-management.png"


class ProviderCloudPortalService(_Management):
    _icon = "provider-cloud-portal-service.png"


class PushNotifications(_Management):
    _icon = "push-notifications.png"


class ServiceManagementTools(_Management):
    _icon = "service-management-tools.png"


# Aliases
