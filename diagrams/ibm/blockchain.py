# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _IBM


class _Blockchain(_IBM):
    _type = "blockchain"
    _icon_dir = "resources/ibm/blockchain"


class BlockchainDeveloper(_Blockchain):
    _icon = "blockchain-developer.png"


class Blockchain(_Blockchain):
    _icon = "blockchain.png"


class CertificateAuthority(_Blockchain):
    _icon = "certificate-authority.png"


class ClientApplication(_Blockchain):
    _icon = "client-application.png"


class Communication(_Blockchain):
    _icon = "communication.png"


class Consensus(_Blockchain):
    _icon = "consensus.png"


class EventListener(_Blockchain):
    _icon = "event-listener.png"


class Event(_Blockchain):
    _icon = "event.png"


class ExistingEnterpriseSystems(_Blockchain):
    _icon = "existing-enterprise-systems.png"


class HyperledgerFabric(_Blockchain):
    _icon = "hyperledger-fabric.png"


class KeyManagement(_Blockchain):
    _icon = "key-management.png"


class Ledger(_Blockchain):
    _icon = "ledger.png"


class MembershipServicesProviderApi(_Blockchain):
    _icon = "membership-services-provider-api.png"


class Membership(_Blockchain):
    _icon = "membership.png"


class MessageBus(_Blockchain):
    _icon = "message-bus.png"


class Node(_Blockchain):
    _icon = "node.png"


class Services(_Blockchain):
    _icon = "services.png"


class SmartContract(_Blockchain):
    _icon = "smart-contract.png"


class TransactionManager(_Blockchain):
    _icon = "transaction-manager.png"


class Wallet(_Blockchain):
    _icon = "wallet.png"


# Aliases
