# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _IBM


class _Devops(_IBM):
    _type = "devops"
    _icon_dir = "resources/ibm/devops"


class ArtifactManagement(_Devops):
    _icon = "artifact-management.png"


class BuildTest(_Devops):
    _icon = "build-test.png"


class CodeEditor(_Devops):
    _icon = "code-editor.png"


class CollaborativeDevelopment(_Devops):
    _icon = "collaborative-development.png"


class ConfigurationManagement(_Devops):
    _icon = "configuration-management.png"


class ContinuousDeploy(_Devops):
    _icon = "continuous-deploy.png"


class ContinuousTesting(_Devops):
    _icon = "continuous-testing.png"


class Devops(_Devops):
    _icon = "devops.png"


class Provision(_Devops):
    _icon = "provision.png"


class ReleaseManagement(_Devops):
    _icon = "release-management.png"


# Aliases
