# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _IBM


class _Analytics(_IBM):
    _type = "analytics"
    _icon_dir = "resources/ibm/analytics"


class Analytics(_Analytics):
    _icon = "analytics.png"


class DataIntegration(_Analytics):
    _icon = "data-integration.png"


class DataRepositories(_Analytics):
    _icon = "data-repositories.png"


class DeviceAnalytics(_Analytics):
    _icon = "device-analytics.png"


class StreamingComputing(_Analytics):
    _icon = "streaming-computing.png"


# Aliases
