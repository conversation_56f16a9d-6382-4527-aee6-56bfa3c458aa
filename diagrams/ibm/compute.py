# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _IBM


class _Compute(_IBM):
    _type = "compute"
    _icon_dir = "resources/ibm/compute"


class BareMetalServer(_Compute):
    _icon = "bare-metal-server.png"


class ImageService(_Compute):
    _icon = "image-service.png"


class Instance(_Compute):
    _icon = "instance.png"


class Key(_Compute):
    _icon = "key.png"


class PowerInstance(_Compute):
    _icon = "power-instance.png"


# Aliases
