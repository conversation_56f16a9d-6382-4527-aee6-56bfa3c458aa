# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _IBM


class _Security(_IBM):
    _type = "security"
    _icon_dir = "resources/ibm/security"


class ApiSecurity(_Security):
    _icon = "api-security.png"


class BlockchainSecurityService(_Security):
    _icon = "blockchain-security-service.png"


class DataSecurity(_Security):
    _icon = "data-security.png"


class Firewall(_Security):
    _icon = "firewall.png"


class Gateway(_Security):
    _icon = "gateway.png"


class GovernanceRiskCompliance(_Security):
    _icon = "governance-risk-compliance.png"


class IdentityAccessManagement(_Security):
    _icon = "identity-access-management.png"


class IdentityProvider(_Security):
    _icon = "identity-provider.png"


class InfrastructureSecurity(_Security):
    _icon = "infrastructure-security.png"


class PhysicalSecurity(_Security):
    _icon = "physical-security.png"


class SecurityMonitoringIntelligence(_Security):
    _icon = "security-monitoring-intelligence.png"


class SecurityServices(_Security):
    _icon = "security-services.png"


class TrustendComputing(_Security):
    _icon = "trustend-computing.png"


class Vpn(_Security):
    _icon = "vpn.png"


# Aliases
