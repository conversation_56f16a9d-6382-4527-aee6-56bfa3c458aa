# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OCI


class _Devops(_OCI):
    _type = "devops"
    _icon_dir = "resources/oci/devops"


class APIGatewayWhite(_Devops):
    _icon = "api-gateway-white.png"


class APIGateway(_Devops):
    _icon = "api-gateway.png"


class APIServiceWhite(_Devops):
    _icon = "api-service-white.png"


class APIService(_Devops):
    _icon = "api-service.png"


class ResourceMgmtWhite(_Devops):
    _icon = "resource-mgmt-white.png"


class ResourceMgmt(_Devops):
    _icon = "resource-mgmt.png"


# Aliases
