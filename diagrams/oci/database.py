# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OCI


class _Database(_OCI):
    _type = "database"
    _icon_dir = "resources/oci/database"


class AutonomousWhite(_Database):
    _icon = "autonomous-white.png"


class Autonomous(_Database):
    _icon = "autonomous.png"


class BigdataServiceWhite(_Database):
    _icon = "bigdata-service-white.png"


class BigdataService(_Database):
    _icon = "bigdata-service.png"


class DatabaseServiceWhite(_Database):
    _icon = "database-service-white.png"


class DatabaseService(_Database):
    _icon = "database-service.png"


class DataflowApacheWhite(_Database):
    _icon = "dataflow-apache-white.png"


class DataflowApache(_Database):
    _icon = "dataflow-apache.png"


class DcatWhite(_Database):
    _icon = "dcat-white.png"


class Dcat(_Database):
    _icon = "dcat.png"


class DisWhite(_Database):
    _icon = "dis-white.png"


class Dis(_Database):
    _icon = "dis.png"


class DMSWhite(_Database):
    _icon = "dms-white.png"


class DMS(_Database):
    _icon = "dms.png"


class ScienceWhite(_Database):
    _icon = "science-white.png"


class Science(_Database):
    _icon = "science.png"


class StreamWhite(_Database):
    _icon = "stream-white.png"


class Stream(_Database):
    _icon = "stream.png"


# Aliases

ADB = Autonomous
ADBWhite = AutonomousWhite
DBService = DatabaseService
DBServiceWhite = DatabaseServiceWhite
