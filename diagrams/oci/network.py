# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OCI


class _Network(_OCI):
    _type = "network"
    _icon_dir = "resources/oci/network"


class DrgWhite(_Network):
    _icon = "drg-white.png"


class Drg(_Network):
    _icon = "drg.png"


class FirewallWhite(_Network):
    _icon = "firewall-white.png"


class Firewall(_Network):
    _icon = "firewall.png"


class InternetGatewayWhite(_Network):
    _icon = "internet-gateway-white.png"


class InternetGateway(_Network):
    _icon = "internet-gateway.png"


class LoadBalancerWhite(_Network):
    _icon = "load-balancer-white.png"


class LoadBalancer(_Network):
    _icon = "load-balancer.png"


class RouteTableWhite(_Network):
    _icon = "route-table-white.png"


class RouteTable(_Network):
    _icon = "route-table.png"


class SecurityListsWhite(_Network):
    _icon = "security-lists-white.png"


class SecurityLists(_Network):
    _icon = "security-lists.png"


class ServiceGatewayWhite(_Network):
    _icon = "service-gateway-white.png"


class ServiceGateway(_Network):
    _icon = "service-gateway.png"


class VcnWhite(_Network):
    _icon = "vcn-white.png"


class Vcn(_Network):
    _icon = "vcn.png"


# Aliases
