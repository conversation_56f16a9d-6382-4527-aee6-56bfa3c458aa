# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OCI


class _Storage(_OCI):
    _type = "storage"
    _icon_dir = "resources/oci/storage"


class BackupRestoreWhite(_Storage):
    _icon = "backup-restore-white.png"


class BackupRestore(_Storage):
    _icon = "backup-restore.png"


class BlockStorageCloneWhite(_Storage):
    _icon = "block-storage-clone-white.png"


class BlockStorageClone(_Storage):
    _icon = "block-storage-clone.png"


class BlockStorageWhite(_Storage):
    _icon = "block-storage-white.png"


class BlockStorage(_Storage):
    _icon = "block-storage.png"


class BucketsWhite(_Storage):
    _icon = "buckets-white.png"


class Buckets(_Storage):
    _icon = "buckets.png"


class DataTransferWhite(_Storage):
    _icon = "data-transfer-white.png"


class DataTransfer(_Storage):
    _icon = "data-transfer.png"


class ElasticPerformanceWhite(_Storage):
    _icon = "elastic-performance-white.png"


class ElasticPerformance(_Storage):
    _icon = "elastic-performance.png"


class FileStorageWhite(_Storage):
    _icon = "file-storage-white.png"


class FileStorage(_Storage):
    _icon = "file-storage.png"


class ObjectStorageWhite(_Storage):
    _icon = "object-storage-white.png"


class ObjectStorage(_Storage):
    _icon = "object-storage.png"


class StorageGatewayWhite(_Storage):
    _icon = "storage-gateway-white.png"


class StorageGateway(_Storage):
    _icon = "storage-gateway.png"


# Aliases
