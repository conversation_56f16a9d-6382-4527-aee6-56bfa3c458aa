# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OCI


class _Connectivity(_OCI):
    _type = "connectivity"
    _icon_dir = "resources/oci/connectivity"


class BackboneWhite(_Connectivity):
    _icon = "backbone-white.png"


class Backbone(_Connectivity):
    _icon = "backbone.png"


class CDNWhite(_Connectivity):
    _icon = "cdn-white.png"


class CDN(_Connectivity):
    _icon = "cdn.png"


class CustomerDatacenter(_Connectivity):
    _icon = "customer-datacenter.png"


class CustomerDatacntrWhite(_Connectivity):
    _icon = "customer-datacntr-white.png"


class CustomerPremisesWhite(_Connectivity):
    _icon = "customer-premises-white.png"


class CustomerPremises(_Connectivity):
    _icon = "customer-premises.png"


class DisconnectedRegionsWhite(_Connectivity):
    _icon = "disconnected-regions-white.png"


class DisconnectedRegions(_Connectivity):
    _icon = "disconnected-regions.png"


class DNSWhite(_Connectivity):
    _icon = "dns-white.png"


class DNS(_Connectivity):
    _icon = "dns.png"


class FastConnectWhite(_Connectivity):
    _icon = "fast-connect-white.png"


class FastConnect(_Connectivity):
    _icon = "fast-connect.png"


class NATGatewayWhite(_Connectivity):
    _icon = "nat-gateway-white.png"


class NATGateway(_Connectivity):
    _icon = "nat-gateway.png"


class VPNWhite(_Connectivity):
    _icon = "vpn-white.png"


class VPN(_Connectivity):
    _icon = "vpn.png"


# Aliases
