# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OCI


class _Governance(_OCI):
    _type = "governance"
    _icon_dir = "resources/oci/governance"


class AuditWhite(_Governance):
    _icon = "audit-white.png"


class Audit(_Governance):
    _icon = "audit.png"


class CompartmentsWhite(_Governance):
    _icon = "compartments-white.png"


class Compartments(_Governance):
    _icon = "compartments.png"


class GroupsWhite(_Governance):
    _icon = "groups-white.png"


class Groups(_Governance):
    _icon = "groups.png"


class LoggingWhite(_Governance):
    _icon = "logging-white.png"


class Logging(_Governance):
    _icon = "logging.png"


class OCIDWhite(_Governance):
    _icon = "ocid-white.png"


class OCID(_Governance):
    _icon = "ocid.png"


class PoliciesWhite(_Governance):
    _icon = "policies-white.png"


class Policies(_Governance):
    _icon = "policies.png"


class TaggingWhite(_Governance):
    _icon = "tagging-white.png"


class Tagging(_Governance):
    _icon = "tagging.png"


# Aliases
