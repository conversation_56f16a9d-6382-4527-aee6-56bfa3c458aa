# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OCI


class _Security(_OCI):
    _type = "security"
    _icon_dir = "resources/oci/security"


class CloudGuardWhite(_Security):
    _icon = "cloud-guard-white.png"


class CloudGuard(_Security):
    _icon = "cloud-guard.png"


class DDOSWhite(_Security):
    _icon = "ddos-white.png"


class DDOS(_Security):
    _icon = "ddos.png"


class EncryptionWhite(_Security):
    _icon = "encryption-white.png"


class Encryption(_Security):
    _icon = "encryption.png"


class IDAccessWhite(_Security):
    _icon = "id-access-white.png"


class IDAccess(_Security):
    _icon = "id-access.png"


class KeyManagementWhite(_Security):
    _icon = "key-management-white.png"


class KeyManagement(_Security):
    _icon = "key-management.png"


class MaxSecurityZoneWhite(_Security):
    _icon = "max-security-zone-white.png"


class MaxSecurityZone(_Security):
    _icon = "max-security-zone.png"


class VaultWhite(_Security):
    _icon = "vault-white.png"


class Vault(_Security):
    _icon = "vault.png"


class WAFWhite(_Security):
    _icon = "waf-white.png"


class WAF(_Security):
    _icon = "waf.png"


# Aliases
