# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _OCI


class _Monitoring(_OCI):
    _type = "monitoring"
    _icon_dir = "resources/oci/monitoring"


class AlarmWhite(_Monitoring):
    _icon = "alarm-white.png"


class Alarm(_Monitoring):
    _icon = "alarm.png"


class EmailWhite(_Monitoring):
    _icon = "email-white.png"


class Email(_Monitoring):
    _icon = "email.png"


class EventsWhite(_Monitoring):
    _icon = "events-white.png"


class Events(_Monitoring):
    _icon = "events.png"


class HealthCheckWhite(_Monitoring):
    _icon = "health-check-white.png"


class HealthCheck(_Monitoring):
    _icon = "health-check.png"


class NotificationsWhite(_Monitoring):
    _icon = "notifications-white.png"


class Notifications(_Monitoring):
    _icon = "notifications.png"


class QueueWhite(_Monitoring):
    _icon = "queue-white.png"


class Queue(_Monitoring):
    _icon = "queue.png"


class SearchWhite(_Monitoring):
    _icon = "search-white.png"


class Search(_Monitoring):
    _icon = "search.png"


class TelemetryWhite(_Monitoring):
    _icon = "telemetry-white.png"


class Telemetry(_Monitoring):
    _icon = "telemetry.png"


class WorkflowWhite(_Monitoring):
    _icon = "workflow-white.png"


class Workflow(_Monitoring):
    _icon = "workflow.png"


# Aliases
