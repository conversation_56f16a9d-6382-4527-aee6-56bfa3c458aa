# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _K8S


class _Ecosystem(_K8S):
    _type = "ecosystem"
    _icon_dir = "resources/k8s/ecosystem"


class ExternalDns(_Ecosystem):
    _icon = "external-dns.png"


class Helm(_Ecosystem):
    _icon = "helm.png"


class Krew(_Ecosystem):
    _icon = "krew.png"


class Kustomize(_Ecosystem):
    _icon = "kustomize.png"


# Aliases
