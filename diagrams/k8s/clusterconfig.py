# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _K8S


class _Clusterconfig(_K8S):
    _type = "clusterconfig"
    _icon_dir = "resources/k8s/clusterconfig"


class HPA(_Clusterconfig):
    _icon = "hpa.png"


class Limits(_Clusterconfig):
    _icon = "limits.png"


class Quota(_Clusterconfig):
    _icon = "quota.png"


# Aliases

LimitRange = Limits
HorizontalPodAutoscaler = HPA
