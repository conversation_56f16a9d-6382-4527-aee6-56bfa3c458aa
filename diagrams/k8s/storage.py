# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _K8S


class _Storage(_K8S):
    _type = "storage"
    _icon_dir = "resources/k8s/storage"


class PV(_Storage):
    _icon = "pv.png"


class PVC(_Storage):
    _icon = "pvc.png"


class SC(_Storage):
    _icon = "sc.png"


class Vol(_Storage):
    _icon = "vol.png"


# Aliases

PersistentVolume = PV
PersistentVolumeClaim = PVC
StorageClass = SC
Volume = Vol
