# This module is automatically generated by autogen.sh. DO NOT EDIT.

from . import _{{ pvd|up_or_title(pvd) }}

class _{{ pvd|up_or_title(typ) }}(_{{ pvd|up_or_title(pvd) }}):
    _type = "{{ typ }}"
    _icon_dir = "resources/{{ pvd }}/{{ typ }}"

{% for meta in metas %}
class {{ meta["name"] }}(_{{ pvd|up_or_title(typ) }}):
    _icon = "{{ meta["icon"] }}"{% endfor %}

# Aliases
{% for svc, alias in aliases.items() %}
{{ alias }} = {{ svc }}{% endfor %}
