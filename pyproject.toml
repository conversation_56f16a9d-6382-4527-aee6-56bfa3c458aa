[tool.poetry]
name = "diagrams"
version = "0.24.4"
description = "Diagram as Code"
license = "MIT"
authors = ["mingrammer <<EMAIL>>"]
readme = "README.md"
homepage = "https://diagrams.mingrammer.com"
repository = "https://github.com/mingrammer/diagrams"
include = ["resources/**/*"]

[tool.poetry.scripts]
diagrams="diagrams.cli:main"

[tool.poetry.dependencies]
python = "^3.9"
graphviz = ">=0.13.2,<0.21.0"
jinja2 = ">=2.10,<4.0"
typed-ast = {version="^1.5.5", markers="python_version<'3.8'"}
pre-commit = "^4.0.1"

[tool.poetry.dev-dependencies]
pytest = "^8.4"
pylint = "^3.3"
rope = "^1.13"
isort = "^5.13"
black = "^24.4"

[tool.black]
line-length=120
