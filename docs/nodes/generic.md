---
id: generic
title: Generic
---

Node classes list of the generic provider.

## generic.blank


<img width="30" src="/img/resources/generic/blank/blank.png" alt="Blank" style="float: left; padding-right: 5px;" >
**diagrams.generic.blank.Blank**

## generic.compute


<img width="30" src="/img/resources/generic/compute/rack.png" alt="Rack" style="float: left; padding-right: 5px;" >
**diagrams.generic.compute.Rack**

## generic.database


<img width="30" src="/img/resources/generic/database/sql.png" alt="SQL" style="float: left; padding-right: 5px;" >
**diagrams.generic.database.SQL**

## generic.device


<img width="30" src="/img/resources/generic/device/mobile.png" alt="Mobile" style="float: left; padding-right: 5px;" >
**diagrams.generic.device.Mobile**

<img width="30" src="/img/resources/generic/device/tablet.png" alt="Tablet" style="float: left; padding-right: 5px;" >
**diagrams.generic.device.Tablet**

## generic.network


<img width="30" src="/img/resources/generic/network/firewall.png" alt="Firewall" style="float: left; padding-right: 5px;" >
**diagrams.generic.network.Firewall**

<img width="30" src="/img/resources/generic/network/router.png" alt="Router" style="float: left; padding-right: 5px;" >
**diagrams.generic.network.Router**

<img width="30" src="/img/resources/generic/network/subnet.png" alt="Subnet" style="float: left; padding-right: 5px;" >
**diagrams.generic.network.Subnet**

<img width="30" src="/img/resources/generic/network/switch.png" alt="Switch" style="float: left; padding-right: 5px;" >
**diagrams.generic.network.Switch**

<img width="30" src="/img/resources/generic/network/vpn.png" alt="VPN" style="float: left; padding-right: 5px;" >
**diagrams.generic.network.VPN**

## generic.os


<img width="30" src="/img/resources/generic/os/android.png" alt="Android" style="float: left; padding-right: 5px;" >
**diagrams.generic.os.Android**

<img width="30" src="/img/resources/generic/os/centos.png" alt="Centos" style="float: left; padding-right: 5px;" >
**diagrams.generic.os.Centos**

<img width="30" src="/img/resources/generic/os/debian.png" alt="Debian" style="float: left; padding-right: 5px;" >
**diagrams.generic.os.Debian**

<img width="30" src="/img/resources/generic/os/ios.png" alt="IOS" style="float: left; padding-right: 5px;" >
**diagrams.generic.os.IOS**

<img width="30" src="/img/resources/generic/os/linux-general.png" alt="LinuxGeneral" style="float: left; padding-right: 5px;" >
**diagrams.generic.os.LinuxGeneral**

<img width="30" src="/img/resources/generic/os/raspbian.png" alt="Raspbian" style="float: left; padding-right: 5px;" >
**diagrams.generic.os.Raspbian**

<img width="30" src="/img/resources/generic/os/red-hat.png" alt="RedHat" style="float: left; padding-right: 5px;" >
**diagrams.generic.os.RedHat**

<img width="30" src="/img/resources/generic/os/suse.png" alt="Suse" style="float: left; padding-right: 5px;" >
**diagrams.generic.os.Suse**

<img width="30" src="/img/resources/generic/os/ubuntu.png" alt="Ubuntu" style="float: left; padding-right: 5px;" >
**diagrams.generic.os.Ubuntu**

<img width="30" src="/img/resources/generic/os/windows.png" alt="Windows" style="float: left; padding-right: 5px;" >
**diagrams.generic.os.Windows**

## generic.place


<img width="30" src="/img/resources/generic/place/datacenter.png" alt="Datacenter" style="float: left; padding-right: 5px;" >
**diagrams.generic.place.Datacenter**

## generic.storage


<img width="30" src="/img/resources/generic/storage/storage.png" alt="Storage" style="float: left; padding-right: 5px;" >
**diagrams.generic.storage.Storage**

## generic.virtualization


<img width="30" src="/img/resources/generic/virtualization/qemu.png" alt="Qemu" style="float: left; padding-right: 5px;" >
**diagrams.generic.virtualization.Qemu**

<img width="30" src="/img/resources/generic/virtualization/virtualbox.png" alt="Virtualbox" style="float: left; padding-right: 5px;" >
**diagrams.generic.virtualization.Virtualbox**

<img width="30" src="/img/resources/generic/virtualization/vmware.png" alt="Vmware" style="float: left; padding-right: 5px;" >
**diagrams.generic.virtualization.Vmware**

<img width="30" src="/img/resources/generic/virtualization/xen.png" alt="XEN" style="float: left; padding-right: 5px;" >
**diagrams.generic.virtualization.XEN**
