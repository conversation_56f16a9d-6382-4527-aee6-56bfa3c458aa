---
id: gcp
title: GCP
---

Node classes list of the gcp provider.

## gcp.analytics


<img width="30" src="/img/resources/gcp/analytics/bigquery.png" alt="Bigquery" style="float: left; padding-right: 5px;" >
**diagrams.gcp.analytics.Bigquery**, **BigQuery** (alias)

<img width="30" src="/img/resources/gcp/analytics/composer.png" alt="Composer" style="float: left; padding-right: 5px;" >
**diagrams.gcp.analytics.Composer**

<img width="30" src="/img/resources/gcp/analytics/data-catalog.png" alt="DataCatalog" style="float: left; padding-right: 5px;" >
**diagrams.gcp.analytics.DataCatalog**

<img width="30" src="/img/resources/gcp/analytics/data-fusion.png" alt="DataFusion" style="float: left; padding-right: 5px;" >
**diagrams.gcp.analytics.DataFusion**

<img width="30" src="/img/resources/gcp/analytics/dataflow.png" alt="Dataflow" style="float: left; padding-right: 5px;" >
**diagrams.gcp.analytics.Dataflow**

<img width="30" src="/img/resources/gcp/analytics/datalab.png" alt="Datalab" style="float: left; padding-right: 5px;" >
**diagrams.gcp.analytics.Datalab**

<img width="30" src="/img/resources/gcp/analytics/dataprep.png" alt="Dataprep" style="float: left; padding-right: 5px;" >
**diagrams.gcp.analytics.Dataprep**

<img width="30" src="/img/resources/gcp/analytics/dataproc.png" alt="Dataproc" style="float: left; padding-right: 5px;" >
**diagrams.gcp.analytics.Dataproc**

<img width="30" src="/img/resources/gcp/analytics/genomics.png" alt="Genomics" style="float: left; padding-right: 5px;" >
**diagrams.gcp.analytics.Genomics**

<img width="30" src="/img/resources/gcp/analytics/pubsub.png" alt="Pubsub" style="float: left; padding-right: 5px;" >
**diagrams.gcp.analytics.Pubsub**, **PubSub** (alias)

## gcp.api


<img width="30" src="/img/resources/gcp/api/api-gateway.png" alt="APIGateway" style="float: left; padding-right: 5px;" >
**diagrams.gcp.api.APIGateway**

<img width="30" src="/img/resources/gcp/api/apigee.png" alt="Apigee" style="float: left; padding-right: 5px;" >
**diagrams.gcp.api.Apigee**

<img width="30" src="/img/resources/gcp/api/endpoints.png" alt="Endpoints" style="float: left; padding-right: 5px;" >
**diagrams.gcp.api.Endpoints**

## gcp.compute


<img width="30" src="/img/resources/gcp/compute/app-engine.png" alt="AppEngine" style="float: left; padding-right: 5px;" >
**diagrams.gcp.compute.AppEngine**, **GAE** (alias)

<img width="30" src="/img/resources/gcp/compute/compute-engine.png" alt="ComputeEngine" style="float: left; padding-right: 5px;" >
**diagrams.gcp.compute.ComputeEngine**, **GCE** (alias)

<img width="30" src="/img/resources/gcp/compute/container-optimized-os.png" alt="ContainerOptimizedOS" style="float: left; padding-right: 5px;" >
**diagrams.gcp.compute.ContainerOptimizedOS**

<img width="30" src="/img/resources/gcp/compute/functions.png" alt="Functions" style="float: left; padding-right: 5px;" >
**diagrams.gcp.compute.Functions**, **GCF** (alias)

<img width="30" src="/img/resources/gcp/compute/gke-on-prem.png" alt="GKEOnPrem" style="float: left; padding-right: 5px;" >
**diagrams.gcp.compute.GKEOnPrem**

<img width="30" src="/img/resources/gcp/compute/gpu.png" alt="GPU" style="float: left; padding-right: 5px;" >
**diagrams.gcp.compute.GPU**

<img width="30" src="/img/resources/gcp/compute/kubernetes-engine.png" alt="KubernetesEngine" style="float: left; padding-right: 5px;" >
**diagrams.gcp.compute.KubernetesEngine**, **GKE** (alias)

<img width="30" src="/img/resources/gcp/compute/run.png" alt="Run" style="float: left; padding-right: 5px;" >
**diagrams.gcp.compute.Run**

## gcp.database


<img width="30" src="/img/resources/gcp/database/bigtable.png" alt="Bigtable" style="float: left; padding-right: 5px;" >
**diagrams.gcp.database.Bigtable**, **BigTable** (alias)

<img width="30" src="/img/resources/gcp/database/datastore.png" alt="Datastore" style="float: left; padding-right: 5px;" >
**diagrams.gcp.database.Datastore**

<img width="30" src="/img/resources/gcp/database/firestore.png" alt="Firestore" style="float: left; padding-right: 5px;" >
**diagrams.gcp.database.Firestore**

<img width="30" src="/img/resources/gcp/database/memorystore.png" alt="Memorystore" style="float: left; padding-right: 5px;" >
**diagrams.gcp.database.Memorystore**

<img width="30" src="/img/resources/gcp/database/spanner.png" alt="Spanner" style="float: left; padding-right: 5px;" >
**diagrams.gcp.database.Spanner**

<img width="30" src="/img/resources/gcp/database/sql.png" alt="SQL" style="float: left; padding-right: 5px;" >
**diagrams.gcp.database.SQL**

## gcp.devtools


<img width="30" src="/img/resources/gcp/devtools/build.png" alt="Build" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.Build**

<img width="30" src="/img/resources/gcp/devtools/code-for-intellij.png" alt="CodeForIntellij" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.CodeForIntellij**

<img width="30" src="/img/resources/gcp/devtools/code.png" alt="Code" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.Code**

<img width="30" src="/img/resources/gcp/devtools/container-registry.png" alt="ContainerRegistry" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.ContainerRegistry**, **GCR** (alias)

<img width="30" src="/img/resources/gcp/devtools/gradle-app-engine-plugin.png" alt="GradleAppEnginePlugin" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.GradleAppEnginePlugin**

<img width="30" src="/img/resources/gcp/devtools/ide-plugins.png" alt="IdePlugins" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.IdePlugins**

<img width="30" src="/img/resources/gcp/devtools/maven-app-engine-plugin.png" alt="MavenAppEnginePlugin" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.MavenAppEnginePlugin**

<img width="30" src="/img/resources/gcp/devtools/scheduler.png" alt="Scheduler" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.Scheduler**

<img width="30" src="/img/resources/gcp/devtools/sdk.png" alt="SDK" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.SDK**

<img width="30" src="/img/resources/gcp/devtools/source-repositories.png" alt="SourceRepositories" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.SourceRepositories**

<img width="30" src="/img/resources/gcp/devtools/tasks.png" alt="Tasks" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.Tasks**

<img width="30" src="/img/resources/gcp/devtools/test-lab.png" alt="TestLab" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.TestLab**

<img width="30" src="/img/resources/gcp/devtools/tools-for-eclipse.png" alt="ToolsForEclipse" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.ToolsForEclipse**

<img width="30" src="/img/resources/gcp/devtools/tools-for-powershell.png" alt="ToolsForPowershell" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.ToolsForPowershell**

<img width="30" src="/img/resources/gcp/devtools/tools-for-visual-studio.png" alt="ToolsForVisualStudio" style="float: left; padding-right: 5px;" >
**diagrams.gcp.devtools.ToolsForVisualStudio**

## gcp.iot


<img width="30" src="/img/resources/gcp/iot/iot-core.png" alt="IotCore" style="float: left; padding-right: 5px;" >
**diagrams.gcp.iot.IotCore**

## gcp.migration


<img width="30" src="/img/resources/gcp/migration/transfer-appliance.png" alt="TransferAppliance" style="float: left; padding-right: 5px;" >
**diagrams.gcp.migration.TransferAppliance**

## gcp.ml


<img width="30" src="/img/resources/gcp/ml/advanced-solutions-lab.png" alt="AdvancedSolutionsLab" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.AdvancedSolutionsLab**

<img width="30" src="/img/resources/gcp/ml/ai-hub.png" alt="AIHub" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.AIHub**

<img width="30" src="/img/resources/gcp/ml/ai-platform-data-labeling-service.png" alt="AIPlatformDataLabelingService" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.AIPlatformDataLabelingService**

<img width="30" src="/img/resources/gcp/ml/ai-platform.png" alt="AIPlatform" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.AIPlatform**

<img width="30" src="/img/resources/gcp/ml/automl-natural-language.png" alt="AutomlNaturalLanguage" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.AutomlNaturalLanguage**

<img width="30" src="/img/resources/gcp/ml/automl-tables.png" alt="AutomlTables" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.AutomlTables**

<img width="30" src="/img/resources/gcp/ml/automl-translation.png" alt="AutomlTranslation" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.AutomlTranslation**

<img width="30" src="/img/resources/gcp/ml/automl-video-intelligence.png" alt="AutomlVideoIntelligence" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.AutomlVideoIntelligence**

<img width="30" src="/img/resources/gcp/ml/automl-vision.png" alt="AutomlVision" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.AutomlVision**

<img width="30" src="/img/resources/gcp/ml/automl.png" alt="Automl" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.Automl**, **AutoML** (alias)

<img width="30" src="/img/resources/gcp/ml/dialog-flow-enterprise-edition.png" alt="DialogFlowEnterpriseEdition" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.DialogFlowEnterpriseEdition**

<img width="30" src="/img/resources/gcp/ml/inference-api.png" alt="InferenceAPI" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.InferenceAPI**

<img width="30" src="/img/resources/gcp/ml/jobs-api.png" alt="JobsAPI" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.JobsAPI**

<img width="30" src="/img/resources/gcp/ml/natural-language-api.png" alt="NaturalLanguageAPI" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.NaturalLanguageAPI**, **NLAPI** (alias)

<img width="30" src="/img/resources/gcp/ml/recommendations-ai.png" alt="RecommendationsAI" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.RecommendationsAI**

<img width="30" src="/img/resources/gcp/ml/speech-to-text.png" alt="SpeechToText" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.SpeechToText**, **STT** (alias)

<img width="30" src="/img/resources/gcp/ml/text-to-speech.png" alt="TextToSpeech" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.TextToSpeech**, **TTS** (alias)

<img width="30" src="/img/resources/gcp/ml/tpu.png" alt="TPU" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.TPU**

<img width="30" src="/img/resources/gcp/ml/translation-api.png" alt="TranslationAPI" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.TranslationAPI**

<img width="30" src="/img/resources/gcp/ml/video-intelligence-api.png" alt="VideoIntelligenceAPI" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.VideoIntelligenceAPI**

<img width="30" src="/img/resources/gcp/ml/vision-api.png" alt="VisionAPI" style="float: left; padding-right: 5px;" >
**diagrams.gcp.ml.VisionAPI**

## gcp.network


<img width="30" src="/img/resources/gcp/network/armor.png" alt="Armor" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.Armor**

<img width="30" src="/img/resources/gcp/network/cdn.png" alt="CDN" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.CDN**

<img width="30" src="/img/resources/gcp/network/dedicated-interconnect.png" alt="DedicatedInterconnect" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.DedicatedInterconnect**

<img width="30" src="/img/resources/gcp/network/dns.png" alt="DNS" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.DNS**

<img width="30" src="/img/resources/gcp/network/external-ip-addresses.png" alt="ExternalIpAddresses" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.ExternalIpAddresses**

<img width="30" src="/img/resources/gcp/network/firewall-rules.png" alt="FirewallRules" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.FirewallRules**

<img width="30" src="/img/resources/gcp/network/load-balancing.png" alt="LoadBalancing" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.LoadBalancing**

<img width="30" src="/img/resources/gcp/network/nat.png" alt="NAT" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.NAT**

<img width="30" src="/img/resources/gcp/network/network.png" alt="Network" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.Network**

<img width="30" src="/img/resources/gcp/network/partner-interconnect.png" alt="PartnerInterconnect" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.PartnerInterconnect**

<img width="30" src="/img/resources/gcp/network/premium-network-tier.png" alt="PremiumNetworkTier" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.PremiumNetworkTier**

<img width="30" src="/img/resources/gcp/network/router.png" alt="Router" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.Router**

<img width="30" src="/img/resources/gcp/network/routes.png" alt="Routes" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.Routes**

<img width="30" src="/img/resources/gcp/network/standard-network-tier.png" alt="StandardNetworkTier" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.StandardNetworkTier**

<img width="30" src="/img/resources/gcp/network/traffic-director.png" alt="TrafficDirector" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.TrafficDirector**

<img width="30" src="/img/resources/gcp/network/virtual-private-cloud.png" alt="VirtualPrivateCloud" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.VirtualPrivateCloud**, **VPC** (alias)

<img width="30" src="/img/resources/gcp/network/vpn.png" alt="VPN" style="float: left; padding-right: 5px;" >
**diagrams.gcp.network.VPN**

## gcp.operations


<img width="30" src="/img/resources/gcp/operations/logging.png" alt="Logging" style="float: left; padding-right: 5px;" >
**diagrams.gcp.operations.Logging**

<img width="30" src="/img/resources/gcp/operations/monitoring.png" alt="Monitoring" style="float: left; padding-right: 5px;" >
**diagrams.gcp.operations.Monitoring**

## gcp.security


<img width="30" src="/img/resources/gcp/security/iam.png" alt="Iam" style="float: left; padding-right: 5px;" >
**diagrams.gcp.security.Iam**

<img width="30" src="/img/resources/gcp/security/iap.png" alt="IAP" style="float: left; padding-right: 5px;" >
**diagrams.gcp.security.IAP**

<img width="30" src="/img/resources/gcp/security/key-management-service.png" alt="KeyManagementService" style="float: left; padding-right: 5px;" >
**diagrams.gcp.security.KeyManagementService**, **KMS** (alias)

<img width="30" src="/img/resources/gcp/security/resource-manager.png" alt="ResourceManager" style="float: left; padding-right: 5px;" >
**diagrams.gcp.security.ResourceManager**

<img width="30" src="/img/resources/gcp/security/security-command-center.png" alt="SecurityCommandCenter" style="float: left; padding-right: 5px;" >
**diagrams.gcp.security.SecurityCommandCenter**, **SCC** (alias)

<img width="30" src="/img/resources/gcp/security/security-scanner.png" alt="SecurityScanner" style="float: left; padding-right: 5px;" >
**diagrams.gcp.security.SecurityScanner**

## gcp.storage


<img width="30" src="/img/resources/gcp/storage/filestore.png" alt="Filestore" style="float: left; padding-right: 5px;" >
**diagrams.gcp.storage.Filestore**

<img width="30" src="/img/resources/gcp/storage/persistent-disk.png" alt="PersistentDisk" style="float: left; padding-right: 5px;" >
**diagrams.gcp.storage.PersistentDisk**

<img width="30" src="/img/resources/gcp/storage/storage.png" alt="Storage" style="float: left; padding-right: 5px;" >
**diagrams.gcp.storage.Storage**, **GCS** (alias)
