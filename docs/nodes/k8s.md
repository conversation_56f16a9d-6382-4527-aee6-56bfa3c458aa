---
id: k8s
title: K8S
---

Node classes list of the k8s provider.

## k8s.chaos


<img width="30" src="/img/resources/k8s/chaos/chaos-mesh.png" alt="ChaosMesh" style="float: left; padding-right: 5px;" >
**diagrams.k8s.chaos.ChaosMesh**

<img width="30" src="/img/resources/k8s/chaos/litmus-chaos.png" alt="LitmusChaos" style="float: left; padding-right: 5px;" >
**diagrams.k8s.chaos.LitmusChaos**

## k8s.clusterconfig


<img width="30" src="/img/resources/k8s/clusterconfig/hpa.png" alt="HPA" style="float: left; padding-right: 5px;" >
**diagrams.k8s.clusterconfig.HPA**, **HorizontalPodAutoscaler** (alias)

<img width="30" src="/img/resources/k8s/clusterconfig/limits.png" alt="Limits" style="float: left; padding-right: 5px;" >
**diagrams.k8s.clusterconfig.Limits**, **LimitRange** (alias)

<img width="30" src="/img/resources/k8s/clusterconfig/quota.png" alt="Quota" style="float: left; padding-right: 5px;" >
**diagrams.k8s.clusterconfig.Quota**

## k8s.compute


<img width="30" src="/img/resources/k8s/compute/cronjob.png" alt="Cronjob" style="float: left; padding-right: 5px;" >
**diagrams.k8s.compute.Cronjob**

<img width="30" src="/img/resources/k8s/compute/deploy.png" alt="Deploy" style="float: left; padding-right: 5px;" >
**diagrams.k8s.compute.Deploy**, **Deployment** (alias)

<img width="30" src="/img/resources/k8s/compute/ds.png" alt="DS" style="float: left; padding-right: 5px;" >
**diagrams.k8s.compute.DS**, **DaemonSet** (alias)

<img width="30" src="/img/resources/k8s/compute/job.png" alt="Job" style="float: left; padding-right: 5px;" >
**diagrams.k8s.compute.Job**

<img width="30" src="/img/resources/k8s/compute/pod.png" alt="Pod" style="float: left; padding-right: 5px;" >
**diagrams.k8s.compute.Pod**

<img width="30" src="/img/resources/k8s/compute/rs.png" alt="RS" style="float: left; padding-right: 5px;" >
**diagrams.k8s.compute.RS**, **ReplicaSet** (alias)

<img width="30" src="/img/resources/k8s/compute/sts.png" alt="STS" style="float: left; padding-right: 5px;" >
**diagrams.k8s.compute.STS**, **StatefulSet** (alias)

## k8s.controlplane


<img width="30" src="/img/resources/k8s/controlplane/api.png" alt="API" style="float: left; padding-right: 5px;" >
**diagrams.k8s.controlplane.API**, **APIServer** (alias)

<img width="30" src="/img/resources/k8s/controlplane/c-c-m.png" alt="CCM" style="float: left; padding-right: 5px;" >
**diagrams.k8s.controlplane.CCM**

<img width="30" src="/img/resources/k8s/controlplane/c-m.png" alt="CM" style="float: left; padding-right: 5px;" >
**diagrams.k8s.controlplane.CM**, **ControllerManager** (alias)

<img width="30" src="/img/resources/k8s/controlplane/k-proxy.png" alt="KProxy" style="float: left; padding-right: 5px;" >
**diagrams.k8s.controlplane.KProxy**, **KubeProxy** (alias)

<img width="30" src="/img/resources/k8s/controlplane/kubelet.png" alt="Kubelet" style="float: left; padding-right: 5px;" >
**diagrams.k8s.controlplane.Kubelet**

<img width="30" src="/img/resources/k8s/controlplane/sched.png" alt="Sched" style="float: left; padding-right: 5px;" >
**diagrams.k8s.controlplane.Sched**, **Scheduler** (alias)

## k8s.ecosystem


<img width="30" src="/img/resources/k8s/ecosystem/external-dns.png" alt="ExternalDns" style="float: left; padding-right: 5px;" >
**diagrams.k8s.ecosystem.ExternalDns**

<img width="30" src="/img/resources/k8s/ecosystem/helm.png" alt="Helm" style="float: left; padding-right: 5px;" >
**diagrams.k8s.ecosystem.Helm**

<img width="30" src="/img/resources/k8s/ecosystem/krew.png" alt="Krew" style="float: left; padding-right: 5px;" >
**diagrams.k8s.ecosystem.Krew**

<img width="30" src="/img/resources/k8s/ecosystem/kustomize.png" alt="Kustomize" style="float: left; padding-right: 5px;" >
**diagrams.k8s.ecosystem.Kustomize**

## k8s.group


<img width="30" src="/img/resources/k8s/group/ns.png" alt="NS" style="float: left; padding-right: 5px;" >
**diagrams.k8s.group.NS**, **Namespace** (alias)

## k8s.infra


<img width="30" src="/img/resources/k8s/infra/etcd.png" alt="ETCD" style="float: left; padding-right: 5px;" >
**diagrams.k8s.infra.ETCD**

<img width="30" src="/img/resources/k8s/infra/master.png" alt="Master" style="float: left; padding-right: 5px;" >
**diagrams.k8s.infra.Master**

<img width="30" src="/img/resources/k8s/infra/node.png" alt="Node" style="float: left; padding-right: 5px;" >
**diagrams.k8s.infra.Node**

## k8s.network


<img width="30" src="/img/resources/k8s/network/ep.png" alt="Ep" style="float: left; padding-right: 5px;" >
**diagrams.k8s.network.Ep**, **Endpoint** (alias)

<img width="30" src="/img/resources/k8s/network/ing.png" alt="Ing" style="float: left; padding-right: 5px;" >
**diagrams.k8s.network.Ing**, **Ingress** (alias)

<img width="30" src="/img/resources/k8s/network/netpol.png" alt="Netpol" style="float: left; padding-right: 5px;" >
**diagrams.k8s.network.Netpol**, **NetworkPolicy** (alias)

<img width="30" src="/img/resources/k8s/network/svc.png" alt="SVC" style="float: left; padding-right: 5px;" >
**diagrams.k8s.network.SVC**, **Service** (alias)

## k8s.others


<img width="30" src="/img/resources/k8s/others/crd.png" alt="CRD" style="float: left; padding-right: 5px;" >
**diagrams.k8s.others.CRD**

<img width="30" src="/img/resources/k8s/others/psp.png" alt="PSP" style="float: left; padding-right: 5px;" >
**diagrams.k8s.others.PSP**

## k8s.podconfig


<img width="30" src="/img/resources/k8s/podconfig/cm.png" alt="CM" style="float: left; padding-right: 5px;" >
**diagrams.k8s.podconfig.CM**, **ConfigMap** (alias)

<img width="30" src="/img/resources/k8s/podconfig/secret.png" alt="Secret" style="float: left; padding-right: 5px;" >
**diagrams.k8s.podconfig.Secret**

## k8s.rbac


<img width="30" src="/img/resources/k8s/rbac/c-role.png" alt="CRole" style="float: left; padding-right: 5px;" >
**diagrams.k8s.rbac.CRole**, **ClusterRole** (alias)

<img width="30" src="/img/resources/k8s/rbac/crb.png" alt="CRB" style="float: left; padding-right: 5px;" >
**diagrams.k8s.rbac.CRB**, **ClusterRoleBinding** (alias)

<img width="30" src="/img/resources/k8s/rbac/group.png" alt="Group" style="float: left; padding-right: 5px;" >
**diagrams.k8s.rbac.Group**

<img width="30" src="/img/resources/k8s/rbac/rb.png" alt="RB" style="float: left; padding-right: 5px;" >
**diagrams.k8s.rbac.RB**, **RoleBinding** (alias)

<img width="30" src="/img/resources/k8s/rbac/role.png" alt="Role" style="float: left; padding-right: 5px;" >
**diagrams.k8s.rbac.Role**

<img width="30" src="/img/resources/k8s/rbac/sa.png" alt="SA" style="float: left; padding-right: 5px;" >
**diagrams.k8s.rbac.SA**, **ServiceAccount** (alias)

<img width="30" src="/img/resources/k8s/rbac/user.png" alt="User" style="float: left; padding-right: 5px;" >
**diagrams.k8s.rbac.User**

## k8s.storage


<img width="30" src="/img/resources/k8s/storage/pv.png" alt="PV" style="float: left; padding-right: 5px;" >
**diagrams.k8s.storage.PV**, **PersistentVolume** (alias)

<img width="30" src="/img/resources/k8s/storage/pvc.png" alt="PVC" style="float: left; padding-right: 5px;" >
**diagrams.k8s.storage.PVC**, **PersistentVolumeClaim** (alias)

<img width="30" src="/img/resources/k8s/storage/sc.png" alt="SC" style="float: left; padding-right: 5px;" >
**diagrams.k8s.storage.SC**, **StorageClass** (alias)

<img width="30" src="/img/resources/k8s/storage/vol.png" alt="Vol" style="float: left; padding-right: 5px;" >
**diagrams.k8s.storage.Vol**, **Volume** (alias)
