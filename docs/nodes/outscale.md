---
id: outscale
title: Outscale
---

Node classes list of the outscale provider.

## outscale.compute


<img width="30" src="/img/resources/outscale/compute/compute.png" alt="Compute" style="float: left; padding-right: 5px;" >
**diagrams.outscale.compute.Compute**

<img width="30" src="/img/resources/outscale/compute/direct-connect.png" alt="DirectConnect" style="float: left; padding-right: 5px;" >
**diagrams.outscale.compute.DirectConnect**

## outscale.network


<img width="30" src="/img/resources/outscale/network/client-vpn.png" alt="ClientVpn" style="float: left; padding-right: 5px;" >
**diagrams.outscale.network.ClientVpn**

<img width="30" src="/img/resources/outscale/network/internet-service.png" alt="InternetService" style="float: left; padding-right: 5px;" >
**diagrams.outscale.network.InternetService**

<img width="30" src="/img/resources/outscale/network/load-balancer.png" alt="LoadBalancer" style="float: left; padding-right: 5px;" >
**diagrams.outscale.network.LoadBalancer**

<img width="30" src="/img/resources/outscale/network/nat-service.png" alt="NatService" style="float: left; padding-right: 5px;" >
**diagrams.outscale.network.NatService**

<img width="30" src="/img/resources/outscale/network/net.png" alt="Net" style="float: left; padding-right: 5px;" >
**diagrams.outscale.network.Net**

<img width="30" src="/img/resources/outscale/network/site-to-site-vpng.png" alt="SiteToSiteVpng" style="float: left; padding-right: 5px;" >
**diagrams.outscale.network.SiteToSiteVpng**

## outscale.security


<img width="30" src="/img/resources/outscale/security/firewall.png" alt="Firewall" style="float: left; padding-right: 5px;" >
**diagrams.outscale.security.Firewall**

<img width="30" src="/img/resources/outscale/security/identity-and-access-management.png" alt="IdentityAndAccessManagement" style="float: left; padding-right: 5px;" >
**diagrams.outscale.security.IdentityAndAccessManagement**

## outscale.storage


<img width="30" src="/img/resources/outscale/storage/simple-storage-service.png" alt="SimpleStorageService" style="float: left; padding-right: 5px;" >
**diagrams.outscale.storage.SimpleStorageService**

<img width="30" src="/img/resources/outscale/storage/storage.png" alt="Storage" style="float: left; padding-right: 5px;" >
**diagrams.outscale.storage.Storage**
