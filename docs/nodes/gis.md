---
id: gis
title: GIS
---

Node classes list of the gis provider.

## gis.cli


<img width="30" src="/img/resources/gis/cli/gdal.png" alt="Gdal" style="float: left; padding-right: 5px;" >
**diagrams.gis.cli.Gdal**

<img width="30" src="/img/resources/gis/cli/imposm.png" alt="Imposm" style="float: left; padding-right: 5px;" >
**diagrams.gis.cli.Imposm**

<img width="30" src="/img/resources/gis/cli/lastools.png" alt="Lastools" style="float: left; padding-right: 5px;" >
**diagrams.gis.cli.Lastools**

<img width="30" src="/img/resources/gis/cli/mapnik.png" alt="Mapnik" style="float: left; padding-right: 5px;" >
**diagrams.gis.cli.Mapnik**

<img width="30" src="/img/resources/gis/cli/mdal.png" alt="Mdal" style="float: left; padding-right: 5px;" >
**diagrams.gis.cli.Mdal**

<img width="30" src="/img/resources/gis/cli/pdal.png" alt="Pdal" style="float: left; padding-right: 5px;" >
**diagrams.gis.cli.Pdal**

## gis.data


<img width="30" src="/img/resources/gis/data/ban.png" alt="BAN" style="float: left; padding-right: 5px;" >
**diagrams.gis.data.BAN**

<img width="30" src="/img/resources/gis/data/here.png" alt="Here" style="float: left; padding-right: 5px;" >
**diagrams.gis.data.Here**

<img width="30" src="/img/resources/gis/data/ign.png" alt="IGN" style="float: left; padding-right: 5px;" >
**diagrams.gis.data.IGN**

<img width="30" src="/img/resources/gis/data/openstreetmap.png" alt="Openstreetmap" style="float: left; padding-right: 5px;" >
**diagrams.gis.data.Openstreetmap**

<img width="30" src="/img/resources/gis/data/overturemaps.png" alt="Overturemaps" style="float: left; padding-right: 5px;" >
**diagrams.gis.data.Overturemaps**

## gis.database


<img width="30" src="/img/resources/gis/database/postgis.png" alt="Postgis" style="float: left; padding-right: 5px;" >
**diagrams.gis.database.Postgis**

## gis.desktop


<img width="30" src="/img/resources/gis/desktop/maptunik.png" alt="Maptunik" style="float: left; padding-right: 5px;" >
**diagrams.gis.desktop.Maptunik**

<img width="30" src="/img/resources/gis/desktop/qgis.png" alt="QGIS" style="float: left; padding-right: 5px;" >
**diagrams.gis.desktop.QGIS**

## gis.format


<img width="30" src="/img/resources/gis/format/geopackage.png" alt="Geopackage" style="float: left; padding-right: 5px;" >
**diagrams.gis.format.Geopackage**

<img width="30" src="/img/resources/gis/format/geoparquet.png" alt="Geoparquet" style="float: left; padding-right: 5px;" >
**diagrams.gis.format.Geoparquet**

## gis.geocoding


<img width="30" src="/img/resources/gis/geocoding/addok.png" alt="Addok" style="float: left; padding-right: 5px;" >
**diagrams.gis.geocoding.Addok**

<img width="30" src="/img/resources/gis/geocoding/gisgraphy.png" alt="Gisgraphy" style="float: left; padding-right: 5px;" >
**diagrams.gis.geocoding.Gisgraphy**

<img width="30" src="/img/resources/gis/geocoding/nominatim.png" alt="Nominatim" style="float: left; padding-right: 5px;" >
**diagrams.gis.geocoding.Nominatim**

<img width="30" src="/img/resources/gis/geocoding/pelias.png" alt="Pelias" style="float: left; padding-right: 5px;" >
**diagrams.gis.geocoding.Pelias**

## gis.georchestra


## gis.java


<img width="30" src="/img/resources/gis/java/geotools.png" alt="Geotools" style="float: left; padding-right: 5px;" >
**diagrams.gis.java.Geotools**

## gis.javascript


<img width="30" src="/img/resources/gis/javascript/cesium.png" alt="Cesium" style="float: left; padding-right: 5px;" >
**diagrams.gis.javascript.Cesium**

<img width="30" src="/img/resources/gis/javascript/geostyler.png" alt="Geostyler" style="float: left; padding-right: 5px;" >
**diagrams.gis.javascript.Geostyler**

<img width="30" src="/img/resources/gis/javascript/keplerjs.png" alt="Keplerjs" style="float: left; padding-right: 5px;" >
**diagrams.gis.javascript.Keplerjs**

<img width="30" src="/img/resources/gis/javascript/leaflet.png" alt="Leaflet" style="float: left; padding-right: 5px;" >
**diagrams.gis.javascript.Leaflet**

<img width="30" src="/img/resources/gis/javascript/maplibre.png" alt="Maplibre" style="float: left; padding-right: 5px;" >
**diagrams.gis.javascript.Maplibre**

<img width="30" src="/img/resources/gis/javascript/ol-ext.png" alt="OlExt" style="float: left; padding-right: 5px;" >
**diagrams.gis.javascript.OlExt**

<img width="30" src="/img/resources/gis/javascript/openlayers.png" alt="Openlayers" style="float: left; padding-right: 5px;" >
**diagrams.gis.javascript.Openlayers**

<img width="30" src="/img/resources/gis/javascript/turfjs.png" alt="Turfjs" style="float: left; padding-right: 5px;" >
**diagrams.gis.javascript.Turfjs**

## gis.mobile


<img width="30" src="/img/resources/gis/mobile/mergin.png" alt="Mergin" style="float: left; padding-right: 5px;" >
**diagrams.gis.mobile.Mergin**

<img width="30" src="/img/resources/gis/mobile/qfield.png" alt="Qfield" style="float: left; padding-right: 5px;" >
**diagrams.gis.mobile.Qfield**

<img width="30" src="/img/resources/gis/mobile/smash.png" alt="Smash" style="float: left; padding-right: 5px;" >
**diagrams.gis.mobile.Smash**

## gis.ogc


<img width="30" src="/img/resources/gis/ogc/ogc.png" alt="OGC" style="float: left; padding-right: 5px;" >
**diagrams.gis.ogc.OGC**

<img width="30" src="/img/resources/gis/ogc/wfs.png" alt="WFS" style="float: left; padding-right: 5px;" >
**diagrams.gis.ogc.WFS**

<img width="30" src="/img/resources/gis/ogc/wms.png" alt="WMS" style="float: left; padding-right: 5px;" >
**diagrams.gis.ogc.WMS**

## gis.organization


<img width="30" src="/img/resources/gis/organization/osgeo.png" alt="Osgeo" style="float: left; padding-right: 5px;" >
**diagrams.gis.organization.Osgeo**

## gis.python


<img width="30" src="/img/resources/gis/python/geopandas.png" alt="Geopandas" style="float: left; padding-right: 5px;" >
**diagrams.gis.python.Geopandas**

<img width="30" src="/img/resources/gis/python/pysal.png" alt="Pysal" style="float: left; padding-right: 5px;" >
**diagrams.gis.python.Pysal**

## gis.routing


<img width="30" src="/img/resources/gis/routing/graphhopper.png" alt="Graphhopper" style="float: left; padding-right: 5px;" >
**diagrams.gis.routing.Graphhopper**

<img width="30" src="/img/resources/gis/routing/osrm.png" alt="Osrm" style="float: left; padding-right: 5px;" >
**diagrams.gis.routing.Osrm**

<img width="30" src="/img/resources/gis/routing/pgrouting.png" alt="Pgrouting" style="float: left; padding-right: 5px;" >
**diagrams.gis.routing.Pgrouting**

<img width="30" src="/img/resources/gis/routing/valhalla.png" alt="Valhalla" style="float: left; padding-right: 5px;" >
**diagrams.gis.routing.Valhalla**

## gis.server


<img width="30" src="/img/resources/gis/server/actinia.png" alt="Actinia" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Actinia**

<img width="30" src="/img/resources/gis/server/baremaps.png" alt="Baremaps" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Baremaps**

<img width="30" src="/img/resources/gis/server/deegree.png" alt="Deegree" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Deegree**

<img width="30" src="/img/resources/gis/server/g3w-suite.png" alt="G3WSuite" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.G3WSuite**

<img width="30" src="/img/resources/gis/server/geohealthcheck.png" alt="Geohealthcheck" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Geohealthcheck**

<img width="30" src="/img/resources/gis/server/geomapfish.png" alt="Geomapfish" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Geomapfish**

<img width="30" src="/img/resources/gis/server/geomesa.png" alt="Geomesa" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Geomesa**

<img width="30" src="/img/resources/gis/server/geonetwork.png" alt="Geonetwork" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Geonetwork**

<img width="30" src="/img/resources/gis/server/geonode.png" alt="Geonode" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Geonode**

<img width="30" src="/img/resources/gis/server/georchestra.png" alt="Georchestra" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Georchestra**

<img width="30" src="/img/resources/gis/server/geoserver.png" alt="Geoserver" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Geoserver**

<img width="30" src="/img/resources/gis/server/geowebcache.png" alt="Geowebcache" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Geowebcache**

<img width="30" src="/img/resources/gis/server/kepler.png" alt="Kepler" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Kepler**

<img width="30" src="/img/resources/gis/server/mapproxy.png" alt="Mapproxy" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Mapproxy**

<img width="30" src="/img/resources/gis/server/mapserver.png" alt="Mapserver" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Mapserver**

<img width="30" src="/img/resources/gis/server/mapstore.png" alt="Mapstore" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Mapstore**

<img width="30" src="/img/resources/gis/server/mviewer.png" alt="Mviewer" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Mviewer**

<img width="30" src="/img/resources/gis/server/pg_tileserv.png" alt="Pg_Tileserv" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Pg_Tileserv**

<img width="30" src="/img/resources/gis/server/pycsw.png" alt="Pycsw" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Pycsw**

<img width="30" src="/img/resources/gis/server/pygeoapi.png" alt="Pygeoapi" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Pygeoapi**

<img width="30" src="/img/resources/gis/server/qgis-server.png" alt="QGISServer" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.QGISServer**

<img width="30" src="/img/resources/gis/server/zooproject.png" alt="Zooproject" style="float: left; padding-right: 5px;" >
**diagrams.gis.server.Zooproject**
