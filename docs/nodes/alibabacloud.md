---
id: alibabacloud
title: AlibabaCloud
---

Node classes list of the alibabacloud provider.

## alibabacloud.analytics


<img width="30" src="/img/resources/alibabacloud/analytics/analytic-db.png" alt="AnalyticDb" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.analytics.AnalyticDb**

<img width="30" src="/img/resources/alibabacloud/analytics/click-house.png" alt="ClickHouse" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.analytics.ClickHouse**

<img width="30" src="/img/resources/alibabacloud/analytics/data-lake-analytics.png" alt="DataLakeAnalytics" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.analytics.DataLakeAnalytics**

<img width="30" src="/img/resources/alibabacloud/analytics/elatic-map-reduce.png" alt="ElaticMapReduce" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.analytics.ElaticMapReduce**

<img width="30" src="/img/resources/alibabacloud/analytics/open-search.png" alt="OpenSearch" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.analytics.OpenSearch**

## alibabacloud.application


<img width="30" src="/img/resources/alibabacloud/application/api-gateway.png" alt="ApiGateway" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.ApiGateway**

<img width="30" src="/img/resources/alibabacloud/application/bee-bot.png" alt="BeeBot" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.BeeBot**

<img width="30" src="/img/resources/alibabacloud/application/blockchain-as-a-service.png" alt="BlockchainAsAService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.BlockchainAsAService**

<img width="30" src="/img/resources/alibabacloud/application/cloud-call-center.png" alt="CloudCallCenter" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.CloudCallCenter**

<img width="30" src="/img/resources/alibabacloud/application/code-pipeline.png" alt="CodePipeline" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.CodePipeline**

<img width="30" src="/img/resources/alibabacloud/application/direct-mail.png" alt="DirectMail" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.DirectMail**

<img width="30" src="/img/resources/alibabacloud/application/log-service.png" alt="LogService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.LogService**, **SLS** (alias)

<img width="30" src="/img/resources/alibabacloud/application/message-notification-service.png" alt="MessageNotificationService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.MessageNotificationService**, **MNS** (alias)

<img width="30" src="/img/resources/alibabacloud/application/node-js-performance-platform.png" alt="NodeJsPerformancePlatform" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.NodeJsPerformancePlatform**

<img width="30" src="/img/resources/alibabacloud/application/open-search.png" alt="OpenSearch" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.OpenSearch**

<img width="30" src="/img/resources/alibabacloud/application/performance-testing-service.png" alt="PerformanceTestingService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.PerformanceTestingService**, **PTS** (alias)

<img width="30" src="/img/resources/alibabacloud/application/rd-cloud.png" alt="RdCloud" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.RdCloud**

<img width="30" src="/img/resources/alibabacloud/application/smart-conversation-analysis.png" alt="SmartConversationAnalysis" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.SmartConversationAnalysis**, **SCA** (alias)

<img width="30" src="/img/resources/alibabacloud/application/yida.png" alt="Yida" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.application.Yida**

## alibabacloud.communication


<img width="30" src="/img/resources/alibabacloud/communication/direct-mail.png" alt="DirectMail" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.communication.DirectMail**

<img width="30" src="/img/resources/alibabacloud/communication/mobile-push.png" alt="MobilePush" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.communication.MobilePush**

## alibabacloud.compute


<img width="30" src="/img/resources/alibabacloud/compute/auto-scaling.png" alt="AutoScaling" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.AutoScaling**, **ESS** (alias)

<img width="30" src="/img/resources/alibabacloud/compute/batch-compute.png" alt="BatchCompute" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.BatchCompute**

<img width="30" src="/img/resources/alibabacloud/compute/container-registry.png" alt="ContainerRegistry" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.ContainerRegistry**

<img width="30" src="/img/resources/alibabacloud/compute/container-service.png" alt="ContainerService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.ContainerService**

<img width="30" src="/img/resources/alibabacloud/compute/elastic-compute-service.png" alt="ElasticComputeService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.ElasticComputeService**, **ECS** (alias)

<img width="30" src="/img/resources/alibabacloud/compute/elastic-container-instance.png" alt="ElasticContainerInstance" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.ElasticContainerInstance**, **ECI** (alias)

<img width="30" src="/img/resources/alibabacloud/compute/elastic-high-performance-computing.png" alt="ElasticHighPerformanceComputing" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.ElasticHighPerformanceComputing**, **EHPC** (alias)

<img width="30" src="/img/resources/alibabacloud/compute/elastic-search.png" alt="ElasticSearch" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.ElasticSearch**

<img width="30" src="/img/resources/alibabacloud/compute/function-compute.png" alt="FunctionCompute" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.FunctionCompute**, **FC** (alias)

<img width="30" src="/img/resources/alibabacloud/compute/operation-orchestration-service.png" alt="OperationOrchestrationService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.OperationOrchestrationService**, **OOS** (alias)

<img width="30" src="/img/resources/alibabacloud/compute/resource-orchestration-service.png" alt="ResourceOrchestrationService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.ResourceOrchestrationService**, **ROS** (alias)

<img width="30" src="/img/resources/alibabacloud/compute/server-load-balancer.png" alt="ServerLoadBalancer" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.ServerLoadBalancer**, **SLB** (alias)

<img width="30" src="/img/resources/alibabacloud/compute/serverless-app-engine.png" alt="ServerlessAppEngine" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.ServerlessAppEngine**, **SAE** (alias)

<img width="30" src="/img/resources/alibabacloud/compute/simple-application-server.png" alt="SimpleApplicationServer" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.SimpleApplicationServer**, **SAS** (alias)

<img width="30" src="/img/resources/alibabacloud/compute/web-app-service.png" alt="WebAppService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.compute.WebAppService**, **WAS** (alias)

## alibabacloud.database


<img width="30" src="/img/resources/alibabacloud/database/apsaradb-cassandra.png" alt="ApsaradbCassandra" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.ApsaradbCassandra**

<img width="30" src="/img/resources/alibabacloud/database/apsaradb-hbase.png" alt="ApsaradbHbase" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.ApsaradbHbase**

<img width="30" src="/img/resources/alibabacloud/database/apsaradb-memcache.png" alt="ApsaradbMemcache" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.ApsaradbMemcache**

<img width="30" src="/img/resources/alibabacloud/database/apsaradb-mongodb.png" alt="ApsaradbMongodb" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.ApsaradbMongodb**

<img width="30" src="/img/resources/alibabacloud/database/apsaradb-oceanbase.png" alt="ApsaradbOceanbase" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.ApsaradbOceanbase**

<img width="30" src="/img/resources/alibabacloud/database/apsaradb-polardb.png" alt="ApsaradbPolardb" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.ApsaradbPolardb**

<img width="30" src="/img/resources/alibabacloud/database/apsaradb-postgresql.png" alt="ApsaradbPostgresql" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.ApsaradbPostgresql**

<img width="30" src="/img/resources/alibabacloud/database/apsaradb-ppas.png" alt="ApsaradbPpas" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.ApsaradbPpas**

<img width="30" src="/img/resources/alibabacloud/database/apsaradb-redis.png" alt="ApsaradbRedis" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.ApsaradbRedis**

<img width="30" src="/img/resources/alibabacloud/database/apsaradb-sqlserver.png" alt="ApsaradbSqlserver" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.ApsaradbSqlserver**

<img width="30" src="/img/resources/alibabacloud/database/data-management-service.png" alt="DataManagementService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.DataManagementService**, **DMS** (alias)

<img width="30" src="/img/resources/alibabacloud/database/data-transmission-service.png" alt="DataTransmissionService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.DataTransmissionService**, **DTS** (alias)

<img width="30" src="/img/resources/alibabacloud/database/database-backup-service.png" alt="DatabaseBackupService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.DatabaseBackupService**, **DBS** (alias)

<img width="30" src="/img/resources/alibabacloud/database/disribute-relational-database-service.png" alt="DisributeRelationalDatabaseService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.DisributeRelationalDatabaseService**, **DRDS** (alias)

<img width="30" src="/img/resources/alibabacloud/database/graph-database-service.png" alt="GraphDatabaseService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.GraphDatabaseService**, **GDS** (alias)

<img width="30" src="/img/resources/alibabacloud/database/hybriddb-for-mysql.png" alt="HybriddbForMysql" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.HybriddbForMysql**

<img width="30" src="/img/resources/alibabacloud/database/relational-database-service.png" alt="RelationalDatabaseService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.database.RelationalDatabaseService**, **RDS** (alias)

## alibabacloud.iot


<img width="30" src="/img/resources/alibabacloud/iot/iot-internet-device-id.png" alt="IotInternetDeviceId" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.iot.IotInternetDeviceId**

<img width="30" src="/img/resources/alibabacloud/iot/iot-link-wan.png" alt="IotLinkWan" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.iot.IotLinkWan**

<img width="30" src="/img/resources/alibabacloud/iot/iot-mobile-connection-package.png" alt="IotMobileConnectionPackage" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.iot.IotMobileConnectionPackage**

<img width="30" src="/img/resources/alibabacloud/iot/iot-platform.png" alt="IotPlatform" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.iot.IotPlatform**

## alibabacloud.network


<img width="30" src="/img/resources/alibabacloud/network/cdn.png" alt="Cdn" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.network.Cdn**

<img width="30" src="/img/resources/alibabacloud/network/cloud-enterprise-network.png" alt="CloudEnterpriseNetwork" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.network.CloudEnterpriseNetwork**, **CEN** (alias)

<img width="30" src="/img/resources/alibabacloud/network/elastic-ip-address.png" alt="ElasticIpAddress" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.network.ElasticIpAddress**, **EIP** (alias)

<img width="30" src="/img/resources/alibabacloud/network/express-connect.png" alt="ExpressConnect" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.network.ExpressConnect**

<img width="30" src="/img/resources/alibabacloud/network/nat-gateway.png" alt="NatGateway" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.network.NatGateway**

<img width="30" src="/img/resources/alibabacloud/network/server-load-balancer.png" alt="ServerLoadBalancer" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.network.ServerLoadBalancer**, **SLB** (alias)

<img width="30" src="/img/resources/alibabacloud/network/smart-access-gateway.png" alt="SmartAccessGateway" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.network.SmartAccessGateway**

<img width="30" src="/img/resources/alibabacloud/network/virtual-private-cloud.png" alt="VirtualPrivateCloud" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.network.VirtualPrivateCloud**, **VPC** (alias)

<img width="30" src="/img/resources/alibabacloud/network/vpn-gateway.png" alt="VpnGateway" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.network.VpnGateway**

## alibabacloud.security


<img width="30" src="/img/resources/alibabacloud/security/anti-bot-service.png" alt="AntiBotService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.AntiBotService**, **ABS** (alias)

<img width="30" src="/img/resources/alibabacloud/security/anti-ddos-basic.png" alt="AntiDdosBasic" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.AntiDdosBasic**

<img width="30" src="/img/resources/alibabacloud/security/anti-ddos-pro.png" alt="AntiDdosPro" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.AntiDdosPro**

<img width="30" src="/img/resources/alibabacloud/security/antifraud-service.png" alt="AntifraudService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.AntifraudService**, **AS** (alias)

<img width="30" src="/img/resources/alibabacloud/security/bastion-host.png" alt="BastionHost" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.BastionHost**

<img width="30" src="/img/resources/alibabacloud/security/cloud-firewall.png" alt="CloudFirewall" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.CloudFirewall**, **CFW** (alias)

<img width="30" src="/img/resources/alibabacloud/security/cloud-security-scanner.png" alt="CloudSecurityScanner" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.CloudSecurityScanner**

<img width="30" src="/img/resources/alibabacloud/security/content-moderation.png" alt="ContentModeration" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.ContentModeration**, **CM** (alias)

<img width="30" src="/img/resources/alibabacloud/security/crowdsourced-security-testing.png" alt="CrowdsourcedSecurityTesting" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.CrowdsourcedSecurityTesting**

<img width="30" src="/img/resources/alibabacloud/security/data-encryption-service.png" alt="DataEncryptionService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.DataEncryptionService**, **DES** (alias)

<img width="30" src="/img/resources/alibabacloud/security/db-audit.png" alt="DbAudit" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.DbAudit**

<img width="30" src="/img/resources/alibabacloud/security/game-shield.png" alt="GameShield" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.GameShield**

<img width="30" src="/img/resources/alibabacloud/security/id-verification.png" alt="IdVerification" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.IdVerification**

<img width="30" src="/img/resources/alibabacloud/security/managed-security-service.png" alt="ManagedSecurityService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.ManagedSecurityService**

<img width="30" src="/img/resources/alibabacloud/security/security-center.png" alt="SecurityCenter" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.SecurityCenter**

<img width="30" src="/img/resources/alibabacloud/security/server-guard.png" alt="ServerGuard" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.ServerGuard**

<img width="30" src="/img/resources/alibabacloud/security/ssl-certificates.png" alt="SslCertificates" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.SslCertificates**

<img width="30" src="/img/resources/alibabacloud/security/web-application-firewall.png" alt="WebApplicationFirewall" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.security.WebApplicationFirewall**, **WAF** (alias)

## alibabacloud.storage


<img width="30" src="/img/resources/alibabacloud/storage/cloud-storage-gateway.png" alt="CloudStorageGateway" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.storage.CloudStorageGateway**

<img width="30" src="/img/resources/alibabacloud/storage/file-storage-hdfs.png" alt="FileStorageHdfs" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.storage.FileStorageHdfs**, **HDFS** (alias)

<img width="30" src="/img/resources/alibabacloud/storage/file-storage-nas.png" alt="FileStorageNas" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.storage.FileStorageNas**, **NAS** (alias)

<img width="30" src="/img/resources/alibabacloud/storage/hybrid-backup-recovery.png" alt="HybridBackupRecovery" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.storage.HybridBackupRecovery**, **HBR** (alias)

<img width="30" src="/img/resources/alibabacloud/storage/hybrid-cloud-disaster-recovery.png" alt="HybridCloudDisasterRecovery" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.storage.HybridCloudDisasterRecovery**, **HDR** (alias)

<img width="30" src="/img/resources/alibabacloud/storage/imm.png" alt="Imm" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.storage.Imm**

<img width="30" src="/img/resources/alibabacloud/storage/object-storage-service.png" alt="ObjectStorageService" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.storage.ObjectStorageService**, **OSS** (alias)

<img width="30" src="/img/resources/alibabacloud/storage/object-table-store.png" alt="ObjectTableStore" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.storage.ObjectTableStore**, **OTS** (alias)

## alibabacloud.web


<img width="30" src="/img/resources/alibabacloud/web/dns.png" alt="Dns" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.web.Dns**

<img width="30" src="/img/resources/alibabacloud/web/domain.png" alt="Domain" style="float: left; padding-right: 5px;" >
**diagrams.alibabacloud.web.Domain**
