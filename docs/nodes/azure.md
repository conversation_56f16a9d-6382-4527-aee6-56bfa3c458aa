---
id: azure
title: Azure
---

Node classes list of the azure provider.

## azure.analytics


<img width="30" src="/img/resources/azure/analytics/analysis-services.png" alt="AnalysisServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.analytics.AnalysisServices**

<img width="30" src="/img/resources/azure/analytics/data-explorer-clusters.png" alt="DataExplorerClusters" style="float: left; padding-right: 5px;" >
**diagrams.azure.analytics.DataExplorerClusters**

<img width="30" src="/img/resources/azure/analytics/data-factories.png" alt="DataFactories" style="float: left; padding-right: 5px;" >
**diagrams.azure.analytics.DataFactories**

<img width="30" src="/img/resources/azure/analytics/data-lake-analytics.png" alt="DataLakeAnalytics" style="float: left; padding-right: 5px;" >
**diagrams.azure.analytics.DataLakeAnalytics**

<img width="30" src="/img/resources/azure/analytics/data-lake-store-gen1.png" alt="DataLakeStoreGen1" style="float: left; padding-right: 5px;" >
**diagrams.azure.analytics.DataLakeStoreGen1**

<img width="30" src="/img/resources/azure/analytics/databricks.png" alt="Databricks" style="float: left; padding-right: 5px;" >
**diagrams.azure.analytics.Databricks**

<img width="30" src="/img/resources/azure/analytics/event-hub-clusters.png" alt="EventHubClusters" style="float: left; padding-right: 5px;" >
**diagrams.azure.analytics.EventHubClusters**

<img width="30" src="/img/resources/azure/analytics/event-hubs.png" alt="EventHubs" style="float: left; padding-right: 5px;" >
**diagrams.azure.analytics.EventHubs**

<img width="30" src="/img/resources/azure/analytics/hdinsightclusters.png" alt="Hdinsightclusters" style="float: left; padding-right: 5px;" >
**diagrams.azure.analytics.Hdinsightclusters**

<img width="30" src="/img/resources/azure/analytics/log-analytics-workspaces.png" alt="LogAnalyticsWorkspaces" style="float: left; padding-right: 5px;" >
**diagrams.azure.analytics.LogAnalyticsWorkspaces**

<img width="30" src="/img/resources/azure/analytics/stream-analytics-jobs.png" alt="StreamAnalyticsJobs" style="float: left; padding-right: 5px;" >
**diagrams.azure.analytics.StreamAnalyticsJobs**

<img width="30" src="/img/resources/azure/analytics/synapse-analytics.png" alt="SynapseAnalytics" style="float: left; padding-right: 5px;" >
**diagrams.azure.analytics.SynapseAnalytics**

## azure.compute


<img width="30" src="/img/resources/azure/compute/app-services.png" alt="AppServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.AppServices**

<img width="30" src="/img/resources/azure/compute/automanaged-vm.png" alt="AutomanagedVM" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.AutomanagedVM**

<img width="30" src="/img/resources/azure/compute/availability-sets.png" alt="AvailabilitySets" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.AvailabilitySets**

<img width="30" src="/img/resources/azure/compute/batch-accounts.png" alt="BatchAccounts" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.BatchAccounts**

<img width="30" src="/img/resources/azure/compute/citrix-virtual-desktops-essentials.png" alt="CitrixVirtualDesktopsEssentials" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.CitrixVirtualDesktopsEssentials**

<img width="30" src="/img/resources/azure/compute/cloud-services-classic.png" alt="CloudServicesClassic" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.CloudServicesClassic**

<img width="30" src="/img/resources/azure/compute/cloud-services.png" alt="CloudServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.CloudServices**

<img width="30" src="/img/resources/azure/compute/cloudsimple-virtual-machines.png" alt="CloudsimpleVirtualMachines" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.CloudsimpleVirtualMachines**

<img width="30" src="/img/resources/azure/compute/container-apps.png" alt="ContainerApps" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.ContainerApps**

<img width="30" src="/img/resources/azure/compute/container-instances.png" alt="ContainerInstances" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.ContainerInstances**

<img width="30" src="/img/resources/azure/compute/container-registries.png" alt="ContainerRegistries" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.ContainerRegistries**, **ACR** (alias)

<img width="30" src="/img/resources/azure/compute/disk-encryption-sets.png" alt="DiskEncryptionSets" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.DiskEncryptionSets**

<img width="30" src="/img/resources/azure/compute/disk-snapshots.png" alt="DiskSnapshots" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.DiskSnapshots**

<img width="30" src="/img/resources/azure/compute/disks.png" alt="Disks" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.Disks**

<img width="30" src="/img/resources/azure/compute/function-apps.png" alt="FunctionApps" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.FunctionApps**

<img width="30" src="/img/resources/azure/compute/image-definitions.png" alt="ImageDefinitions" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.ImageDefinitions**

<img width="30" src="/img/resources/azure/compute/image-versions.png" alt="ImageVersions" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.ImageVersions**

<img width="30" src="/img/resources/azure/compute/kubernetes-services.png" alt="KubernetesServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.KubernetesServices**, **AKS** (alias)

<img width="30" src="/img/resources/azure/compute/mesh-applications.png" alt="MeshApplications" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.MeshApplications**

<img width="30" src="/img/resources/azure/compute/os-images.png" alt="OsImages" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.OsImages**

<img width="30" src="/img/resources/azure/compute/sap-hana-on-azure.png" alt="SAPHANAOnAzure" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.SAPHANAOnAzure**

<img width="30" src="/img/resources/azure/compute/service-fabric-clusters.png" alt="ServiceFabricClusters" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.ServiceFabricClusters**

<img width="30" src="/img/resources/azure/compute/shared-image-galleries.png" alt="SharedImageGalleries" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.SharedImageGalleries**

<img width="30" src="/img/resources/azure/compute/spring-cloud.png" alt="SpringCloud" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.SpringCloud**

<img width="30" src="/img/resources/azure/compute/vm-classic.png" alt="VMClassic" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.VMClassic**

<img width="30" src="/img/resources/azure/compute/vm-images.png" alt="VMImages" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.VMImages**

<img width="30" src="/img/resources/azure/compute/vm-linux.png" alt="VMLinux" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.VMLinux**

<img width="30" src="/img/resources/azure/compute/vm-scale-set.png" alt="VMScaleSet" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.VMScaleSet**, **VMSS** (alias)

<img width="30" src="/img/resources/azure/compute/vm-windows.png" alt="VMWindows" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.VMWindows**

<img width="30" src="/img/resources/azure/compute/vm.png" alt="VM" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.VM**

<img width="30" src="/img/resources/azure/compute/workspaces.png" alt="Workspaces" style="float: left; padding-right: 5px;" >
**diagrams.azure.compute.Workspaces**

## azure.database


<img width="30" src="/img/resources/azure/database/blob-storage.png" alt="BlobStorage" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.BlobStorage**

<img width="30" src="/img/resources/azure/database/cache-for-redis.png" alt="CacheForRedis" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.CacheForRedis**

<img width="30" src="/img/resources/azure/database/cosmos-db.png" alt="CosmosDb" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.CosmosDb**

<img width="30" src="/img/resources/azure/database/data-explorer-clusters.png" alt="DataExplorerClusters" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.DataExplorerClusters**

<img width="30" src="/img/resources/azure/database/data-factory.png" alt="DataFactory" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.DataFactory**

<img width="30" src="/img/resources/azure/database/data-lake.png" alt="DataLake" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.DataLake**

<img width="30" src="/img/resources/azure/database/database-for-mariadb-servers.png" alt="DatabaseForMariadbServers" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.DatabaseForMariadbServers**

<img width="30" src="/img/resources/azure/database/database-for-mysql-servers.png" alt="DatabaseForMysqlServers" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.DatabaseForMysqlServers**

<img width="30" src="/img/resources/azure/database/database-for-postgresql-servers.png" alt="DatabaseForPostgresqlServers" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.DatabaseForPostgresqlServers**

<img width="30" src="/img/resources/azure/database/elastic-database-pools.png" alt="ElasticDatabasePools" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.ElasticDatabasePools**

<img width="30" src="/img/resources/azure/database/elastic-job-agents.png" alt="ElasticJobAgents" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.ElasticJobAgents**

<img width="30" src="/img/resources/azure/database/instance-pools.png" alt="InstancePools" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.InstancePools**

<img width="30" src="/img/resources/azure/database/managed-databases.png" alt="ManagedDatabases" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.ManagedDatabases**

<img width="30" src="/img/resources/azure/database/sql-databases.png" alt="SQLDatabases" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.SQLDatabases**

<img width="30" src="/img/resources/azure/database/sql-datawarehouse.png" alt="SQLDatawarehouse" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.SQLDatawarehouse**

<img width="30" src="/img/resources/azure/database/sql-managed-instances.png" alt="SQLManagedInstances" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.SQLManagedInstances**

<img width="30" src="/img/resources/azure/database/sql-server-stretch-databases.png" alt="SQLServerStretchDatabases" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.SQLServerStretchDatabases**

<img width="30" src="/img/resources/azure/database/sql-servers.png" alt="SQLServers" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.SQLServers**

<img width="30" src="/img/resources/azure/database/sql-vm.png" alt="SQLVM" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.SQLVM**

<img width="30" src="/img/resources/azure/database/sql.png" alt="SQL" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.SQL**

<img width="30" src="/img/resources/azure/database/ssis-lift-and-shift-ir.png" alt="SsisLiftAndShiftIr" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.SsisLiftAndShiftIr**

<img width="30" src="/img/resources/azure/database/synapse-analytics.png" alt="SynapseAnalytics" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.SynapseAnalytics**

<img width="30" src="/img/resources/azure/database/virtual-clusters.png" alt="VirtualClusters" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.VirtualClusters**

<img width="30" src="/img/resources/azure/database/virtual-datacenter.png" alt="VirtualDatacenter" style="float: left; padding-right: 5px;" >
**diagrams.azure.database.VirtualDatacenter**

## azure.devops


<img width="30" src="/img/resources/azure/devops/application-insights.png" alt="ApplicationInsights" style="float: left; padding-right: 5px;" >
**diagrams.azure.devops.ApplicationInsights**

<img width="30" src="/img/resources/azure/devops/artifacts.png" alt="Artifacts" style="float: left; padding-right: 5px;" >
**diagrams.azure.devops.Artifacts**

<img width="30" src="/img/resources/azure/devops/boards.png" alt="Boards" style="float: left; padding-right: 5px;" >
**diagrams.azure.devops.Boards**

<img width="30" src="/img/resources/azure/devops/devops.png" alt="Devops" style="float: left; padding-right: 5px;" >
**diagrams.azure.devops.Devops**

<img width="30" src="/img/resources/azure/devops/devtest-labs.png" alt="DevtestLabs" style="float: left; padding-right: 5px;" >
**diagrams.azure.devops.DevtestLabs**

<img width="30" src="/img/resources/azure/devops/lab-services.png" alt="LabServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.devops.LabServices**

<img width="30" src="/img/resources/azure/devops/pipelines.png" alt="Pipelines" style="float: left; padding-right: 5px;" >
**diagrams.azure.devops.Pipelines**

<img width="30" src="/img/resources/azure/devops/repos.png" alt="Repos" style="float: left; padding-right: 5px;" >
**diagrams.azure.devops.Repos**

<img width="30" src="/img/resources/azure/devops/test-plans.png" alt="TestPlans" style="float: left; padding-right: 5px;" >
**diagrams.azure.devops.TestPlans**

## azure.general


<img width="30" src="/img/resources/azure/general/allresources.png" alt="Allresources" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Allresources**

<img width="30" src="/img/resources/azure/general/azurehome.png" alt="Azurehome" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Azurehome**

<img width="30" src="/img/resources/azure/general/developertools.png" alt="Developertools" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Developertools**

<img width="30" src="/img/resources/azure/general/helpsupport.png" alt="Helpsupport" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Helpsupport**

<img width="30" src="/img/resources/azure/general/information.png" alt="Information" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Information**

<img width="30" src="/img/resources/azure/general/managementgroups.png" alt="Managementgroups" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Managementgroups**

<img width="30" src="/img/resources/azure/general/marketplace.png" alt="Marketplace" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Marketplace**

<img width="30" src="/img/resources/azure/general/quickstartcenter.png" alt="Quickstartcenter" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Quickstartcenter**

<img width="30" src="/img/resources/azure/general/recent.png" alt="Recent" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Recent**

<img width="30" src="/img/resources/azure/general/reservations.png" alt="Reservations" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Reservations**

<img width="30" src="/img/resources/azure/general/resource.png" alt="Resource" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Resource**

<img width="30" src="/img/resources/azure/general/resourcegroups.png" alt="Resourcegroups" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Resourcegroups**

<img width="30" src="/img/resources/azure/general/servicehealth.png" alt="Servicehealth" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Servicehealth**

<img width="30" src="/img/resources/azure/general/shareddashboard.png" alt="Shareddashboard" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Shareddashboard**

<img width="30" src="/img/resources/azure/general/subscriptions.png" alt="Subscriptions" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Subscriptions**

<img width="30" src="/img/resources/azure/general/support.png" alt="Support" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Support**

<img width="30" src="/img/resources/azure/general/supportrequests.png" alt="Supportrequests" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Supportrequests**

<img width="30" src="/img/resources/azure/general/tag.png" alt="Tag" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Tag**

<img width="30" src="/img/resources/azure/general/tags.png" alt="Tags" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Tags**

<img width="30" src="/img/resources/azure/general/templates.png" alt="Templates" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Templates**

<img width="30" src="/img/resources/azure/general/twousericon.png" alt="Twousericon" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Twousericon**

<img width="30" src="/img/resources/azure/general/userhealthicon.png" alt="Userhealthicon" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Userhealthicon**

<img width="30" src="/img/resources/azure/general/usericon.png" alt="Usericon" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Usericon**

<img width="30" src="/img/resources/azure/general/userprivacy.png" alt="Userprivacy" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Userprivacy**

<img width="30" src="/img/resources/azure/general/userresource.png" alt="Userresource" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Userresource**

<img width="30" src="/img/resources/azure/general/whatsnew.png" alt="Whatsnew" style="float: left; padding-right: 5px;" >
**diagrams.azure.general.Whatsnew**

## azure.identity


<img width="30" src="/img/resources/azure/identity/access-review.png" alt="AccessReview" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.AccessReview**

<img width="30" src="/img/resources/azure/identity/active-directory-connect-health.png" alt="ActiveDirectoryConnectHealth" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.ActiveDirectoryConnectHealth**

<img width="30" src="/img/resources/azure/identity/active-directory.png" alt="ActiveDirectory" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.ActiveDirectory**

<img width="30" src="/img/resources/azure/identity/ad-b2c.png" alt="ADB2C" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.ADB2C**

<img width="30" src="/img/resources/azure/identity/ad-domain-services.png" alt="ADDomainServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.ADDomainServices**

<img width="30" src="/img/resources/azure/identity/ad-identity-protection.png" alt="ADIdentityProtection" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.ADIdentityProtection**

<img width="30" src="/img/resources/azure/identity/ad-privileged-identity-management.png" alt="ADPrivilegedIdentityManagement" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.ADPrivilegedIdentityManagement**

<img width="30" src="/img/resources/azure/identity/app-registrations.png" alt="AppRegistrations" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.AppRegistrations**

<img width="30" src="/img/resources/azure/identity/conditional-access.png" alt="ConditionalAccess" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.ConditionalAccess**

<img width="30" src="/img/resources/azure/identity/enterprise-applications.png" alt="EnterpriseApplications" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.EnterpriseApplications**

<img width="30" src="/img/resources/azure/identity/groups.png" alt="Groups" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.Groups**

<img width="30" src="/img/resources/azure/identity/identity-governance.png" alt="IdentityGovernance" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.IdentityGovernance**

<img width="30" src="/img/resources/azure/identity/information-protection.png" alt="InformationProtection" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.InformationProtection**

<img width="30" src="/img/resources/azure/identity/managed-identities.png" alt="ManagedIdentities" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.ManagedIdentities**

<img width="30" src="/img/resources/azure/identity/users.png" alt="Users" style="float: left; padding-right: 5px;" >
**diagrams.azure.identity.Users**

## azure.integration


<img width="30" src="/img/resources/azure/integration/api-for-fhir.png" alt="APIForFhir" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.APIForFhir**

<img width="30" src="/img/resources/azure/integration/api-management.png" alt="APIManagement" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.APIManagement**

<img width="30" src="/img/resources/azure/integration/app-configuration.png" alt="AppConfiguration" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.AppConfiguration**

<img width="30" src="/img/resources/azure/integration/data-catalog.png" alt="DataCatalog" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.DataCatalog**

<img width="30" src="/img/resources/azure/integration/event-grid-domains.png" alt="EventGridDomains" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.EventGridDomains**

<img width="30" src="/img/resources/azure/integration/event-grid-subscriptions.png" alt="EventGridSubscriptions" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.EventGridSubscriptions**

<img width="30" src="/img/resources/azure/integration/event-grid-topics.png" alt="EventGridTopics" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.EventGridTopics**

<img width="30" src="/img/resources/azure/integration/integration-accounts.png" alt="IntegrationAccounts" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.IntegrationAccounts**

<img width="30" src="/img/resources/azure/integration/integration-service-environments.png" alt="IntegrationServiceEnvironments" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.IntegrationServiceEnvironments**

<img width="30" src="/img/resources/azure/integration/logic-apps-custom-connector.png" alt="LogicAppsCustomConnector" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.LogicAppsCustomConnector**

<img width="30" src="/img/resources/azure/integration/logic-apps.png" alt="LogicApps" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.LogicApps**

<img width="30" src="/img/resources/azure/integration/partner-topic.png" alt="PartnerTopic" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.PartnerTopic**

<img width="30" src="/img/resources/azure/integration/sendgrid-accounts.png" alt="SendgridAccounts" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.SendgridAccounts**

<img width="30" src="/img/resources/azure/integration/service-bus-relays.png" alt="ServiceBusRelays" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.ServiceBusRelays**

<img width="30" src="/img/resources/azure/integration/service-bus.png" alt="ServiceBus" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.ServiceBus**

<img width="30" src="/img/resources/azure/integration/service-catalog-managed-application-definitions.png" alt="ServiceCatalogManagedApplicationDefinitions" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.ServiceCatalogManagedApplicationDefinitions**

<img width="30" src="/img/resources/azure/integration/software-as-a-service.png" alt="SoftwareAsAService" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.SoftwareAsAService**

<img width="30" src="/img/resources/azure/integration/storsimple-device-managers.png" alt="StorsimpleDeviceManagers" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.StorsimpleDeviceManagers**

<img width="30" src="/img/resources/azure/integration/system-topic.png" alt="SystemTopic" style="float: left; padding-right: 5px;" >
**diagrams.azure.integration.SystemTopic**

## azure.iot


<img width="30" src="/img/resources/azure/iot/device-provisioning-services.png" alt="DeviceProvisioningServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.iot.DeviceProvisioningServices**

<img width="30" src="/img/resources/azure/iot/digital-twins.png" alt="DigitalTwins" style="float: left; padding-right: 5px;" >
**diagrams.azure.iot.DigitalTwins**

<img width="30" src="/img/resources/azure/iot/iot-central-applications.png" alt="IotCentralApplications" style="float: left; padding-right: 5px;" >
**diagrams.azure.iot.IotCentralApplications**

<img width="30" src="/img/resources/azure/iot/iot-hub-security.png" alt="IotHubSecurity" style="float: left; padding-right: 5px;" >
**diagrams.azure.iot.IotHubSecurity**

<img width="30" src="/img/resources/azure/iot/iot-hub.png" alt="IotHub" style="float: left; padding-right: 5px;" >
**diagrams.azure.iot.IotHub**

<img width="30" src="/img/resources/azure/iot/maps.png" alt="Maps" style="float: left; padding-right: 5px;" >
**diagrams.azure.iot.Maps**

<img width="30" src="/img/resources/azure/iot/sphere.png" alt="Sphere" style="float: left; padding-right: 5px;" >
**diagrams.azure.iot.Sphere**

<img width="30" src="/img/resources/azure/iot/time-series-insights-environments.png" alt="TimeSeriesInsightsEnvironments" style="float: left; padding-right: 5px;" >
**diagrams.azure.iot.TimeSeriesInsightsEnvironments**

<img width="30" src="/img/resources/azure/iot/time-series-insights-events-sources.png" alt="TimeSeriesInsightsEventsSources" style="float: left; padding-right: 5px;" >
**diagrams.azure.iot.TimeSeriesInsightsEventsSources**

<img width="30" src="/img/resources/azure/iot/windows-10-iot-core-services.png" alt="Windows10IotCoreServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.iot.Windows10IotCoreServices**

## azure.migration


<img width="30" src="/img/resources/azure/migration/data-box-edge.png" alt="DataBoxEdge" style="float: left; padding-right: 5px;" >
**diagrams.azure.migration.DataBoxEdge**

<img width="30" src="/img/resources/azure/migration/data-box.png" alt="DataBox" style="float: left; padding-right: 5px;" >
**diagrams.azure.migration.DataBox**

<img width="30" src="/img/resources/azure/migration/database-migration-services.png" alt="DatabaseMigrationServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.migration.DatabaseMigrationServices**

<img width="30" src="/img/resources/azure/migration/migration-projects.png" alt="MigrationProjects" style="float: left; padding-right: 5px;" >
**diagrams.azure.migration.MigrationProjects**

<img width="30" src="/img/resources/azure/migration/recovery-services-vaults.png" alt="RecoveryServicesVaults" style="float: left; padding-right: 5px;" >
**diagrams.azure.migration.RecoveryServicesVaults**

## azure.ml


<img width="30" src="/img/resources/azure/ml/azure-open-ai.png" alt="AzureOpenAI" style="float: left; padding-right: 5px;" >
**diagrams.azure.ml.AzureOpenAI**

<img width="30" src="/img/resources/azure/ml/azure-speech-service.png" alt="AzureSpeechService" style="float: left; padding-right: 5px;" >
**diagrams.azure.ml.AzureSpeechService**

<img width="30" src="/img/resources/azure/ml/batch-ai.png" alt="BatchAI" style="float: left; padding-right: 5px;" >
**diagrams.azure.ml.BatchAI**

<img width="30" src="/img/resources/azure/ml/bot-services.png" alt="BotServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.ml.BotServices**

<img width="30" src="/img/resources/azure/ml/cognitive-services.png" alt="CognitiveServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.ml.CognitiveServices**

<img width="30" src="/img/resources/azure/ml/genomics-accounts.png" alt="GenomicsAccounts" style="float: left; padding-right: 5px;" >
**diagrams.azure.ml.GenomicsAccounts**

<img width="30" src="/img/resources/azure/ml/machine-learning-service-workspaces.png" alt="MachineLearningServiceWorkspaces" style="float: left; padding-right: 5px;" >
**diagrams.azure.ml.MachineLearningServiceWorkspaces**

<img width="30" src="/img/resources/azure/ml/machine-learning-studio-web-service-plans.png" alt="MachineLearningStudioWebServicePlans" style="float: left; padding-right: 5px;" >
**diagrams.azure.ml.MachineLearningStudioWebServicePlans**

<img width="30" src="/img/resources/azure/ml/machine-learning-studio-web-services.png" alt="MachineLearningStudioWebServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.ml.MachineLearningStudioWebServices**

<img width="30" src="/img/resources/azure/ml/machine-learning-studio-workspaces.png" alt="MachineLearningStudioWorkspaces" style="float: left; padding-right: 5px;" >
**diagrams.azure.ml.MachineLearningStudioWorkspaces**

## azure.mobile


<img width="30" src="/img/resources/azure/mobile/app-service-mobile.png" alt="AppServiceMobile" style="float: left; padding-right: 5px;" >
**diagrams.azure.mobile.AppServiceMobile**

<img width="30" src="/img/resources/azure/mobile/mobile-engagement.png" alt="MobileEngagement" style="float: left; padding-right: 5px;" >
**diagrams.azure.mobile.MobileEngagement**

<img width="30" src="/img/resources/azure/mobile/notification-hubs.png" alt="NotificationHubs" style="float: left; padding-right: 5px;" >
**diagrams.azure.mobile.NotificationHubs**

## azure.monitor


<img width="30" src="/img/resources/azure/monitor/change-analysis.png" alt="ChangeAnalysis" style="float: left; padding-right: 5px;" >
**diagrams.azure.monitor.ChangeAnalysis**

<img width="30" src="/img/resources/azure/monitor/logs.png" alt="Logs" style="float: left; padding-right: 5px;" >
**diagrams.azure.monitor.Logs**

<img width="30" src="/img/resources/azure/monitor/metrics.png" alt="Metrics" style="float: left; padding-right: 5px;" >
**diagrams.azure.monitor.Metrics**

<img width="30" src="/img/resources/azure/monitor/monitor.png" alt="Monitor" style="float: left; padding-right: 5px;" >
**diagrams.azure.monitor.Monitor**

## azure.network


<img width="30" src="/img/resources/azure/network/application-gateway.png" alt="ApplicationGateway" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.ApplicationGateway**

<img width="30" src="/img/resources/azure/network/application-security-groups.png" alt="ApplicationSecurityGroups" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.ApplicationSecurityGroups**

<img width="30" src="/img/resources/azure/network/cdn-profiles.png" alt="CDNProfiles" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.CDNProfiles**

<img width="30" src="/img/resources/azure/network/connections.png" alt="Connections" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.Connections**

<img width="30" src="/img/resources/azure/network/ddos-protection-plans.png" alt="DDOSProtectionPlans" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.DDOSProtectionPlans**

<img width="30" src="/img/resources/azure/network/dns-private-zones.png" alt="DNSPrivateZones" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.DNSPrivateZones**

<img width="30" src="/img/resources/azure/network/dns-zones.png" alt="DNSZones" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.DNSZones**

<img width="30" src="/img/resources/azure/network/expressroute-circuits.png" alt="ExpressrouteCircuits" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.ExpressrouteCircuits**

<img width="30" src="/img/resources/azure/network/firewall.png" alt="Firewall" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.Firewall**

<img width="30" src="/img/resources/azure/network/front-doors.png" alt="FrontDoors" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.FrontDoors**

<img width="30" src="/img/resources/azure/network/load-balancers.png" alt="LoadBalancers" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.LoadBalancers**

<img width="30" src="/img/resources/azure/network/local-network-gateways.png" alt="LocalNetworkGateways" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.LocalNetworkGateways**

<img width="30" src="/img/resources/azure/network/network-interfaces.png" alt="NetworkInterfaces" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.NetworkInterfaces**

<img width="30" src="/img/resources/azure/network/network-security-groups-classic.png" alt="NetworkSecurityGroupsClassic" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.NetworkSecurityGroupsClassic**

<img width="30" src="/img/resources/azure/network/network-watcher.png" alt="NetworkWatcher" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.NetworkWatcher**

<img width="30" src="/img/resources/azure/network/on-premises-data-gateways.png" alt="OnPremisesDataGateways" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.OnPremisesDataGateways**

<img width="30" src="/img/resources/azure/network/private-endpoint.png" alt="PrivateEndpoint" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.PrivateEndpoint**

<img width="30" src="/img/resources/azure/network/public-ip-addresses.png" alt="PublicIpAddresses" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.PublicIpAddresses**

<img width="30" src="/img/resources/azure/network/reserved-ip-addresses-classic.png" alt="ReservedIpAddressesClassic" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.ReservedIpAddressesClassic**

<img width="30" src="/img/resources/azure/network/route-filters.png" alt="RouteFilters" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.RouteFilters**

<img width="30" src="/img/resources/azure/network/route-tables.png" alt="RouteTables" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.RouteTables**

<img width="30" src="/img/resources/azure/network/service-endpoint-policies.png" alt="ServiceEndpointPolicies" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.ServiceEndpointPolicies**

<img width="30" src="/img/resources/azure/network/subnets.png" alt="Subnets" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.Subnets**

<img width="30" src="/img/resources/azure/network/traffic-manager-profiles.png" alt="TrafficManagerProfiles" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.TrafficManagerProfiles**

<img width="30" src="/img/resources/azure/network/virtual-network-classic.png" alt="VirtualNetworkClassic" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.VirtualNetworkClassic**

<img width="30" src="/img/resources/azure/network/virtual-network-gateways.png" alt="VirtualNetworkGateways" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.VirtualNetworkGateways**

<img width="30" src="/img/resources/azure/network/virtual-networks.png" alt="VirtualNetworks" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.VirtualNetworks**

<img width="30" src="/img/resources/azure/network/virtual-wans.png" alt="VirtualWans" style="float: left; padding-right: 5px;" >
**diagrams.azure.network.VirtualWans**

## azure.security


<img width="30" src="/img/resources/azure/security/application-security-groups.png" alt="ApplicationSecurityGroups" style="float: left; padding-right: 5px;" >
**diagrams.azure.security.ApplicationSecurityGroups**

<img width="30" src="/img/resources/azure/security/conditional-access.png" alt="ConditionalAccess" style="float: left; padding-right: 5px;" >
**diagrams.azure.security.ConditionalAccess**

<img width="30" src="/img/resources/azure/security/defender.png" alt="Defender" style="float: left; padding-right: 5px;" >
**diagrams.azure.security.Defender**

<img width="30" src="/img/resources/azure/security/extended-security-updates.png" alt="ExtendedSecurityUpdates" style="float: left; padding-right: 5px;" >
**diagrams.azure.security.ExtendedSecurityUpdates**

<img width="30" src="/img/resources/azure/security/key-vaults.png" alt="KeyVaults" style="float: left; padding-right: 5px;" >
**diagrams.azure.security.KeyVaults**

<img width="30" src="/img/resources/azure/security/security-center.png" alt="SecurityCenter" style="float: left; padding-right: 5px;" >
**diagrams.azure.security.SecurityCenter**

<img width="30" src="/img/resources/azure/security/sentinel.png" alt="Sentinel" style="float: left; padding-right: 5px;" >
**diagrams.azure.security.Sentinel**

## azure.storage


<img width="30" src="/img/resources/azure/storage/archive-storage.png" alt="ArchiveStorage" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.ArchiveStorage**

<img width="30" src="/img/resources/azure/storage/azurefxtedgefiler.png" alt="Azurefxtedgefiler" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.Azurefxtedgefiler**

<img width="30" src="/img/resources/azure/storage/blob-storage.png" alt="BlobStorage" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.BlobStorage**

<img width="30" src="/img/resources/azure/storage/data-box-edge-data-box-gateway.png" alt="DataBoxEdgeDataBoxGateway" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.DataBoxEdgeDataBoxGateway**

<img width="30" src="/img/resources/azure/storage/data-box.png" alt="DataBox" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.DataBox**

<img width="30" src="/img/resources/azure/storage/data-lake-storage.png" alt="DataLakeStorage" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.DataLakeStorage**

<img width="30" src="/img/resources/azure/storage/general-storage.png" alt="GeneralStorage" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.GeneralStorage**

<img width="30" src="/img/resources/azure/storage/netapp-files.png" alt="NetappFiles" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.NetappFiles**

<img width="30" src="/img/resources/azure/storage/queues-storage.png" alt="QueuesStorage" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.QueuesStorage**

<img width="30" src="/img/resources/azure/storage/storage-accounts-classic.png" alt="StorageAccountsClassic" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.StorageAccountsClassic**

<img width="30" src="/img/resources/azure/storage/storage-accounts.png" alt="StorageAccounts" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.StorageAccounts**

<img width="30" src="/img/resources/azure/storage/storage-explorer.png" alt="StorageExplorer" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.StorageExplorer**

<img width="30" src="/img/resources/azure/storage/storage-sync-services.png" alt="StorageSyncServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.StorageSyncServices**

<img width="30" src="/img/resources/azure/storage/storsimple-data-managers.png" alt="StorsimpleDataManagers" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.StorsimpleDataManagers**

<img width="30" src="/img/resources/azure/storage/storsimple-device-managers.png" alt="StorsimpleDeviceManagers" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.StorsimpleDeviceManagers**

<img width="30" src="/img/resources/azure/storage/table-storage.png" alt="TableStorage" style="float: left; padding-right: 5px;" >
**diagrams.azure.storage.TableStorage**

## azure.web


<img width="30" src="/img/resources/azure/web/api-connections.png" alt="APIConnections" style="float: left; padding-right: 5px;" >
**diagrams.azure.web.APIConnections**

<img width="30" src="/img/resources/azure/web/app-service-certificates.png" alt="AppServiceCertificates" style="float: left; padding-right: 5px;" >
**diagrams.azure.web.AppServiceCertificates**

<img width="30" src="/img/resources/azure/web/app-service-domains.png" alt="AppServiceDomains" style="float: left; padding-right: 5px;" >
**diagrams.azure.web.AppServiceDomains**

<img width="30" src="/img/resources/azure/web/app-service-environments.png" alt="AppServiceEnvironments" style="float: left; padding-right: 5px;" >
**diagrams.azure.web.AppServiceEnvironments**

<img width="30" src="/img/resources/azure/web/app-service-plans.png" alt="AppServicePlans" style="float: left; padding-right: 5px;" >
**diagrams.azure.web.AppServicePlans**

<img width="30" src="/img/resources/azure/web/app-services.png" alt="AppServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.web.AppServices**

<img width="30" src="/img/resources/azure/web/media-services.png" alt="MediaServices" style="float: left; padding-right: 5px;" >
**diagrams.azure.web.MediaServices**

<img width="30" src="/img/resources/azure/web/notification-hub-namespaces.png" alt="NotificationHubNamespaces" style="float: left; padding-right: 5px;" >
**diagrams.azure.web.NotificationHubNamespaces**

<img width="30" src="/img/resources/azure/web/search.png" alt="Search" style="float: left; padding-right: 5px;" >
**diagrams.azure.web.Search**

<img width="30" src="/img/resources/azure/web/signalr.png" alt="Signalr" style="float: left; padding-right: 5px;" >
**diagrams.azure.web.Signalr**
