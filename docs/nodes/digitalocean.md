---
id: digitalocean
title: DigitalOcean
---

Node classes list of the digitalocean provider.

## digitalocean.compute


<img width="30" src="/img/resources/digitalocean/compute/containers.png" alt="Containers" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.compute.Containers**

<img width="30" src="/img/resources/digitalocean/compute/docker.png" alt="Docker" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.compute.Docker**

<img width="30" src="/img/resources/digitalocean/compute/droplet-connect.png" alt="DropletConnect" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.compute.DropletConnect**

<img width="30" src="/img/resources/digitalocean/compute/droplet-snapshot.png" alt="DropletSnapshot" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.compute.DropletSnapshot**

<img width="30" src="/img/resources/digitalocean/compute/droplet.png" alt="Droplet" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.compute.Droplet**

<img width="30" src="/img/resources/digitalocean/compute/k8s-cluster.png" alt="K8SCluster" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.compute.K8SCluster**

<img width="30" src="/img/resources/digitalocean/compute/k8s-node-pool.png" alt="K8SNodePool" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.compute.K8SNodePool**

<img width="30" src="/img/resources/digitalocean/compute/k8s-node.png" alt="K8SNode" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.compute.K8SNode**

## digitalocean.database


<img width="30" src="/img/resources/digitalocean/database/dbaas-primary-standby-more.png" alt="DbaasPrimaryStandbyMore" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.database.DbaasPrimaryStandbyMore**

<img width="30" src="/img/resources/digitalocean/database/dbaas-primary.png" alt="DbaasPrimary" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.database.DbaasPrimary**

<img width="30" src="/img/resources/digitalocean/database/dbaas-read-only.png" alt="DbaasReadOnly" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.database.DbaasReadOnly**

<img width="30" src="/img/resources/digitalocean/database/dbaas-standby.png" alt="DbaasStandby" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.database.DbaasStandby**

## digitalocean.network


<img width="30" src="/img/resources/digitalocean/network/certificate.png" alt="Certificate" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.network.Certificate**

<img width="30" src="/img/resources/digitalocean/network/domain-registration.png" alt="DomainRegistration" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.network.DomainRegistration**

<img width="30" src="/img/resources/digitalocean/network/domain.png" alt="Domain" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.network.Domain**

<img width="30" src="/img/resources/digitalocean/network/firewall.png" alt="Firewall" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.network.Firewall**

<img width="30" src="/img/resources/digitalocean/network/floating-ip.png" alt="FloatingIp" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.network.FloatingIp**

<img width="30" src="/img/resources/digitalocean/network/internet-gateway.png" alt="InternetGateway" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.network.InternetGateway**

<img width="30" src="/img/resources/digitalocean/network/load-balancer.png" alt="LoadBalancer" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.network.LoadBalancer**

<img width="30" src="/img/resources/digitalocean/network/managed-vpn.png" alt="ManagedVpn" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.network.ManagedVpn**

<img width="30" src="/img/resources/digitalocean/network/vpc.png" alt="Vpc" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.network.Vpc**

## digitalocean.storage


<img width="30" src="/img/resources/digitalocean/storage/folder.png" alt="Folder" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.storage.Folder**

<img width="30" src="/img/resources/digitalocean/storage/space.png" alt="Space" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.storage.Space**

<img width="30" src="/img/resources/digitalocean/storage/volume-snapshot.png" alt="VolumeSnapshot" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.storage.VolumeSnapshot**

<img width="30" src="/img/resources/digitalocean/storage/volume.png" alt="Volume" style="float: left; padding-right: 5px;" >
**diagrams.digitalocean.storage.Volume**
