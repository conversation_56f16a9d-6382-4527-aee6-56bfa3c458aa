---
id: programming
title: Programming
---

Node classes list of the programming provider.

## programming.flowchart


<img width="30" src="/img/resources/programming/flowchart/action.png" alt="Action" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.Action**

<img width="30" src="/img/resources/programming/flowchart/collate.png" alt="Collate" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.Collate**

<img width="30" src="/img/resources/programming/flowchart/database.png" alt="Database" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.Database**

<img width="30" src="/img/resources/programming/flowchart/decision.png" alt="Decision" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.Decision**

<img width="30" src="/img/resources/programming/flowchart/delay.png" alt="Delay" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.Delay**

<img width="30" src="/img/resources/programming/flowchart/display.png" alt="Display" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.Display**

<img width="30" src="/img/resources/programming/flowchart/document.png" alt="Document" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.Document**

<img width="30" src="/img/resources/programming/flowchart/input-output.png" alt="InputOutput" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.InputOutput**

<img width="30" src="/img/resources/programming/flowchart/inspection.png" alt="Inspection" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.Inspection**

<img width="30" src="/img/resources/programming/flowchart/internal-storage.png" alt="InternalStorage" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.InternalStorage**

<img width="30" src="/img/resources/programming/flowchart/loop-limit.png" alt="LoopLimit" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.LoopLimit**

<img width="30" src="/img/resources/programming/flowchart/manual-input.png" alt="ManualInput" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.ManualInput**

<img width="30" src="/img/resources/programming/flowchart/manual-loop.png" alt="ManualLoop" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.ManualLoop**

<img width="30" src="/img/resources/programming/flowchart/merge.png" alt="Merge" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.Merge**

<img width="30" src="/img/resources/programming/flowchart/multiple-documents.png" alt="MultipleDocuments" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.MultipleDocuments**

<img width="30" src="/img/resources/programming/flowchart/off-page-connector-left.png" alt="OffPageConnectorLeft" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.OffPageConnectorLeft**

<img width="30" src="/img/resources/programming/flowchart/off-page-connector-right.png" alt="OffPageConnectorRight" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.OffPageConnectorRight**

<img width="30" src="/img/resources/programming/flowchart/or.png" alt="Or" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.Or**

<img width="30" src="/img/resources/programming/flowchart/predefined-process.png" alt="PredefinedProcess" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.PredefinedProcess**

<img width="30" src="/img/resources/programming/flowchart/preparation.png" alt="Preparation" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.Preparation**

<img width="30" src="/img/resources/programming/flowchart/sort.png" alt="Sort" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.Sort**

<img width="30" src="/img/resources/programming/flowchart/start-end.png" alt="StartEnd" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.StartEnd**

<img width="30" src="/img/resources/programming/flowchart/stored-data.png" alt="StoredData" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.StoredData**

<img width="30" src="/img/resources/programming/flowchart/summing-junction.png" alt="SummingJunction" style="float: left; padding-right: 5px;" >
**diagrams.programming.flowchart.SummingJunction**

## programming.framework


<img width="30" src="/img/resources/programming/framework/angular.png" alt="Angular" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Angular**

<img width="30" src="/img/resources/programming/framework/backbone.png" alt="Backbone" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Backbone**

<img width="30" src="/img/resources/programming/framework/camel.png" alt="Camel" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Camel**

<img width="30" src="/img/resources/programming/framework/django.png" alt="Django" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Django**

<img width="30" src="/img/resources/programming/framework/dotnet.png" alt="Dotnet" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Dotnet**, **DotNet** (alias)

<img width="30" src="/img/resources/programming/framework/ember.png" alt="Ember" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Ember**

<img width="30" src="/img/resources/programming/framework/fastapi.png" alt="Fastapi" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Fastapi**, **FastAPI** (alias)

<img width="30" src="/img/resources/programming/framework/flask.png" alt="Flask" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Flask**

<img width="30" src="/img/resources/programming/framework/flutter.png" alt="Flutter" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Flutter**

<img width="30" src="/img/resources/programming/framework/graphql.png" alt="Graphql" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Graphql**, **GraphQL** (alias)

<img width="30" src="/img/resources/programming/framework/hibernate.png" alt="Hibernate" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Hibernate**

<img width="30" src="/img/resources/programming/framework/jhipster.png" alt="Jhipster" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Jhipster**

<img width="30" src="/img/resources/programming/framework/laravel.png" alt="Laravel" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Laravel**

<img width="30" src="/img/resources/programming/framework/micronaut.png" alt="Micronaut" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Micronaut**

<img width="30" src="/img/resources/programming/framework/nextjs.png" alt="Nextjs" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Nextjs**, **NextJs** (alias)

<img width="30" src="/img/resources/programming/framework/phoenix.png" alt="Phoenix" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Phoenix**

<img width="30" src="/img/resources/programming/framework/quarkus.png" alt="Quarkus" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Quarkus**

<img width="30" src="/img/resources/programming/framework/rails.png" alt="Rails" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Rails**

<img width="30" src="/img/resources/programming/framework/react.png" alt="React" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.React**

<img width="30" src="/img/resources/programming/framework/spring.png" alt="Spring" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Spring**

<img width="30" src="/img/resources/programming/framework/sqlpage.png" alt="Sqlpage" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Sqlpage**

<img width="30" src="/img/resources/programming/framework/starlette.png" alt="Starlette" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Starlette**

<img width="30" src="/img/resources/programming/framework/svelte.png" alt="Svelte" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Svelte**

<img width="30" src="/img/resources/programming/framework/vercel.png" alt="Vercel" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Vercel**

<img width="30" src="/img/resources/programming/framework/vue.png" alt="Vue" style="float: left; padding-right: 5px;" >
**diagrams.programming.framework.Vue**

## programming.language


<img width="30" src="/img/resources/programming/language/bash.png" alt="Bash" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Bash**

<img width="30" src="/img/resources/programming/language/c.png" alt="C" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.C**

<img width="30" src="/img/resources/programming/language/cpp.png" alt="Cpp" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Cpp**

<img width="30" src="/img/resources/programming/language/csharp.png" alt="Csharp" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Csharp**

<img width="30" src="/img/resources/programming/language/dart.png" alt="Dart" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Dart**

<img width="30" src="/img/resources/programming/language/elixir.png" alt="Elixir" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Elixir**

<img width="30" src="/img/resources/programming/language/erlang.png" alt="Erlang" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Erlang**

<img width="30" src="/img/resources/programming/language/go.png" alt="Go" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Go**

<img width="30" src="/img/resources/programming/language/java.png" alt="Java" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Java**

<img width="30" src="/img/resources/programming/language/javascript.png" alt="Javascript" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Javascript**, **JavaScript** (alias)

<img width="30" src="/img/resources/programming/language/kotlin.png" alt="Kotlin" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Kotlin**

<img width="30" src="/img/resources/programming/language/latex.png" alt="Latex" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Latex**

<img width="30" src="/img/resources/programming/language/matlab.png" alt="Matlab" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Matlab**

<img width="30" src="/img/resources/programming/language/nodejs.png" alt="Nodejs" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Nodejs**, **NodeJS** (alias)

<img width="30" src="/img/resources/programming/language/php.png" alt="Php" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Php**, **PHP** (alias)

<img width="30" src="/img/resources/programming/language/python.png" alt="Python" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Python**

<img width="30" src="/img/resources/programming/language/r.png" alt="R" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.R**

<img width="30" src="/img/resources/programming/language/ruby.png" alt="Ruby" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Ruby**

<img width="30" src="/img/resources/programming/language/rust.png" alt="Rust" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Rust**

<img width="30" src="/img/resources/programming/language/scala.png" alt="Scala" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Scala**

<img width="30" src="/img/resources/programming/language/sql.png" alt="Sql" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Sql**

<img width="30" src="/img/resources/programming/language/swift.png" alt="Swift" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Swift**

<img width="30" src="/img/resources/programming/language/typescript.png" alt="Typescript" style="float: left; padding-right: 5px;" >
**diagrams.programming.language.Typescript**, **TypeScript** (alias)

## programming.runtime


<img width="30" src="/img/resources/programming/runtime/dapr.png" alt="Dapr" style="float: left; padding-right: 5px;" >
**diagrams.programming.runtime.Dapr**
