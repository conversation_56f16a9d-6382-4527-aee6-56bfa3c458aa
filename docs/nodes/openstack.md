---
id: openstack
title: OpenStack
---

Node classes list of the openstack provider.

## openstack.apiproxies


<img width="30" src="/img/resources/openstack/apiproxies/ec2api.png" alt="EC2API" style="float: left; padding-right: 5px;" >
**diagrams.openstack.apiproxies.EC2API**

## openstack.applicationlifecycle


<img width="30" src="/img/resources/openstack/applicationlifecycle/freezer.png" alt="Freezer" style="float: left; padding-right: 5px;" >
**diagrams.openstack.applicationlifecycle.Freezer**

<img width="30" src="/img/resources/openstack/applicationlifecycle/masakari.png" alt="Masakari" style="float: left; padding-right: 5px;" >
**diagrams.openstack.applicationlifecycle.Masakari**

<img width="30" src="/img/resources/openstack/applicationlifecycle/murano.png" alt="Murano" style="float: left; padding-right: 5px;" >
**diagrams.openstack.applicationlifecycle.Murano**

<img width="30" src="/img/resources/openstack/applicationlifecycle/solum.png" alt="Solum" style="float: left; padding-right: 5px;" >
**diagrams.openstack.applicationlifecycle.Solum**

## openstack.baremetal


<img width="30" src="/img/resources/openstack/baremetal/cyborg.png" alt="Cyborg" style="float: left; padding-right: 5px;" >
**diagrams.openstack.baremetal.Cyborg**

<img width="30" src="/img/resources/openstack/baremetal/ironic.png" alt="Ironic" style="float: left; padding-right: 5px;" >
**diagrams.openstack.baremetal.Ironic**

## openstack.billing


<img width="30" src="/img/resources/openstack/billing/cloudkitty.png" alt="Cloudkitty" style="float: left; padding-right: 5px;" >
**diagrams.openstack.billing.Cloudkitty**, **CloudKitty** (alias)

## openstack.compute


<img width="30" src="/img/resources/openstack/compute/nova.png" alt="Nova" style="float: left; padding-right: 5px;" >
**diagrams.openstack.compute.Nova**

<img width="30" src="/img/resources/openstack/compute/qinling.png" alt="Qinling" style="float: left; padding-right: 5px;" >
**diagrams.openstack.compute.Qinling**

<img width="30" src="/img/resources/openstack/compute/zun.png" alt="Zun" style="float: left; padding-right: 5px;" >
**diagrams.openstack.compute.Zun**

## openstack.containerservices


<img width="30" src="/img/resources/openstack/containerservices/kuryr.png" alt="Kuryr" style="float: left; padding-right: 5px;" >
**diagrams.openstack.containerservices.Kuryr**

## openstack.deployment


<img width="30" src="/img/resources/openstack/deployment/ansible.png" alt="Ansible" style="float: left; padding-right: 5px;" >
**diagrams.openstack.deployment.Ansible**

<img width="30" src="/img/resources/openstack/deployment/charms.png" alt="Charms" style="float: left; padding-right: 5px;" >
**diagrams.openstack.deployment.Charms**

<img width="30" src="/img/resources/openstack/deployment/chef.png" alt="Chef" style="float: left; padding-right: 5px;" >
**diagrams.openstack.deployment.Chef**

<img width="30" src="/img/resources/openstack/deployment/helm.png" alt="Helm" style="float: left; padding-right: 5px;" >
**diagrams.openstack.deployment.Helm**

<img width="30" src="/img/resources/openstack/deployment/kolla.png" alt="Kolla" style="float: left; padding-right: 5px;" >
**diagrams.openstack.deployment.Kolla**, **KollaAnsible** (alias)

<img width="30" src="/img/resources/openstack/deployment/tripleo.png" alt="Tripleo" style="float: left; padding-right: 5px;" >
**diagrams.openstack.deployment.Tripleo**, **TripleO** (alias)

## openstack.frontend


<img width="30" src="/img/resources/openstack/frontend/horizon.png" alt="Horizon" style="float: left; padding-right: 5px;" >
**diagrams.openstack.frontend.Horizon**

## openstack.monitoring


<img width="30" src="/img/resources/openstack/monitoring/monasca.png" alt="Monasca" style="float: left; padding-right: 5px;" >
**diagrams.openstack.monitoring.Monasca**

<img width="30" src="/img/resources/openstack/monitoring/telemetry.png" alt="Telemetry" style="float: left; padding-right: 5px;" >
**diagrams.openstack.monitoring.Telemetry**

## openstack.multiregion


<img width="30" src="/img/resources/openstack/multiregion/tricircle.png" alt="Tricircle" style="float: left; padding-right: 5px;" >
**diagrams.openstack.multiregion.Tricircle**

## openstack.networking


<img width="30" src="/img/resources/openstack/networking/designate.png" alt="Designate" style="float: left; padding-right: 5px;" >
**diagrams.openstack.networking.Designate**

<img width="30" src="/img/resources/openstack/networking/neutron.png" alt="Neutron" style="float: left; padding-right: 5px;" >
**diagrams.openstack.networking.Neutron**

<img width="30" src="/img/resources/openstack/networking/octavia.png" alt="Octavia" style="float: left; padding-right: 5px;" >
**diagrams.openstack.networking.Octavia**

## openstack.nfv


<img width="30" src="/img/resources/openstack/nfv/tacker.png" alt="Tacker" style="float: left; padding-right: 5px;" >
**diagrams.openstack.nfv.Tacker**

## openstack.optimization


<img width="30" src="/img/resources/openstack/optimization/congress.png" alt="Congress" style="float: left; padding-right: 5px;" >
**diagrams.openstack.optimization.Congress**

<img width="30" src="/img/resources/openstack/optimization/rally.png" alt="Rally" style="float: left; padding-right: 5px;" >
**diagrams.openstack.optimization.Rally**

<img width="30" src="/img/resources/openstack/optimization/vitrage.png" alt="Vitrage" style="float: left; padding-right: 5px;" >
**diagrams.openstack.optimization.Vitrage**

<img width="30" src="/img/resources/openstack/optimization/watcher.png" alt="Watcher" style="float: left; padding-right: 5px;" >
**diagrams.openstack.optimization.Watcher**

## openstack.orchestration


<img width="30" src="/img/resources/openstack/orchestration/blazar.png" alt="Blazar" style="float: left; padding-right: 5px;" >
**diagrams.openstack.orchestration.Blazar**

<img width="30" src="/img/resources/openstack/orchestration/heat.png" alt="Heat" style="float: left; padding-right: 5px;" >
**diagrams.openstack.orchestration.Heat**

<img width="30" src="/img/resources/openstack/orchestration/mistral.png" alt="Mistral" style="float: left; padding-right: 5px;" >
**diagrams.openstack.orchestration.Mistral**

<img width="30" src="/img/resources/openstack/orchestration/senlin.png" alt="Senlin" style="float: left; padding-right: 5px;" >
**diagrams.openstack.orchestration.Senlin**

<img width="30" src="/img/resources/openstack/orchestration/zaqar.png" alt="Zaqar" style="float: left; padding-right: 5px;" >
**diagrams.openstack.orchestration.Zaqar**

## openstack.packaging


<img width="30" src="/img/resources/openstack/packaging/loci.png" alt="LOCI" style="float: left; padding-right: 5px;" >
**diagrams.openstack.packaging.LOCI**

<img width="30" src="/img/resources/openstack/packaging/puppet.png" alt="Puppet" style="float: left; padding-right: 5px;" >
**diagrams.openstack.packaging.Puppet**

<img width="30" src="/img/resources/openstack/packaging/rpm.png" alt="RPM" style="float: left; padding-right: 5px;" >
**diagrams.openstack.packaging.RPM**

## openstack.sharedservices


<img width="30" src="/img/resources/openstack/sharedservices/barbican.png" alt="Barbican" style="float: left; padding-right: 5px;" >
**diagrams.openstack.sharedservices.Barbican**

<img width="30" src="/img/resources/openstack/sharedservices/glance.png" alt="Glance" style="float: left; padding-right: 5px;" >
**diagrams.openstack.sharedservices.Glance**

<img width="30" src="/img/resources/openstack/sharedservices/karbor.png" alt="Karbor" style="float: left; padding-right: 5px;" >
**diagrams.openstack.sharedservices.Karbor**

<img width="30" src="/img/resources/openstack/sharedservices/keystone.png" alt="Keystone" style="float: left; padding-right: 5px;" >
**diagrams.openstack.sharedservices.Keystone**

<img width="30" src="/img/resources/openstack/sharedservices/searchlight.png" alt="Searchlight" style="float: left; padding-right: 5px;" >
**diagrams.openstack.sharedservices.Searchlight**

## openstack.storage


<img width="30" src="/img/resources/openstack/storage/cinder.png" alt="Cinder" style="float: left; padding-right: 5px;" >
**diagrams.openstack.storage.Cinder**

<img width="30" src="/img/resources/openstack/storage/manila.png" alt="Manila" style="float: left; padding-right: 5px;" >
**diagrams.openstack.storage.Manila**

<img width="30" src="/img/resources/openstack/storage/swift.png" alt="Swift" style="float: left; padding-right: 5px;" >
**diagrams.openstack.storage.Swift**

## openstack.user


<img width="30" src="/img/resources/openstack/user/openstackclient.png" alt="Openstackclient" style="float: left; padding-right: 5px;" >
**diagrams.openstack.user.Openstackclient**, **OpenStackClient** (alias)

## openstack.workloadprovisioning


<img width="30" src="/img/resources/openstack/workloadprovisioning/magnum.png" alt="Magnum" style="float: left; padding-right: 5px;" >
**diagrams.openstack.workloadprovisioning.Magnum**

<img width="30" src="/img/resources/openstack/workloadprovisioning/sahara.png" alt="Sahara" style="float: left; padding-right: 5px;" >
**diagrams.openstack.workloadprovisioning.Sahara**

<img width="30" src="/img/resources/openstack/workloadprovisioning/trove.png" alt="Trove" style="float: left; padding-right: 5px;" >
**diagrams.openstack.workloadprovisioning.Trove**
