---
id: aws
title: AWS
---

Node classes list of the aws provider.

## aws.analytics


<img width="30" src="/img/resources/aws/analytics/amazon-opensearch-service.png" alt="AmazonOpensearchService" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.AmazonOpensearchService**

<img width="30" src="/img/resources/aws/analytics/analytics.png" alt="Analytics" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.Analytics**

<img width="30" src="/img/resources/aws/analytics/athena.png" alt="Athena" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.Athena**

<img width="30" src="/img/resources/aws/analytics/cloudsearch-search-documents.png" alt="CloudsearchSearchDocuments" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.CloudsearchSearchDocuments**

<img width="30" src="/img/resources/aws/analytics/cloudsearch.png" alt="Cloudsearch" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.Cloudsearch**

<img width="30" src="/img/resources/aws/analytics/data-lake-resource.png" alt="DataLakeResource" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.DataLakeResource**

<img width="30" src="/img/resources/aws/analytics/data-pipeline.png" alt="DataPipeline" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.DataPipeline**

<img width="30" src="/img/resources/aws/analytics/elasticsearch-service.png" alt="ElasticsearchService" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.ElasticsearchService**, **ES** (alias)

<img width="30" src="/img/resources/aws/analytics/emr-cluster.png" alt="EMRCluster" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.EMRCluster**

<img width="30" src="/img/resources/aws/analytics/emr-engine-mapr-m3.png" alt="EMREngineMaprM3" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.EMREngineMaprM3**

<img width="30" src="/img/resources/aws/analytics/emr-engine-mapr-m5.png" alt="EMREngineMaprM5" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.EMREngineMaprM5**

<img width="30" src="/img/resources/aws/analytics/emr-engine-mapr-m7.png" alt="EMREngineMaprM7" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.EMREngineMaprM7**

<img width="30" src="/img/resources/aws/analytics/emr-engine.png" alt="EMREngine" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.EMREngine**

<img width="30" src="/img/resources/aws/analytics/emr-hdfs-cluster.png" alt="EMRHdfsCluster" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.EMRHdfsCluster**

<img width="30" src="/img/resources/aws/analytics/emr.png" alt="EMR" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.EMR**

<img width="30" src="/img/resources/aws/analytics/glue-crawlers.png" alt="GlueCrawlers" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.GlueCrawlers**

<img width="30" src="/img/resources/aws/analytics/glue-data-catalog.png" alt="GlueDataCatalog" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.GlueDataCatalog**

<img width="30" src="/img/resources/aws/analytics/glue.png" alt="Glue" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.Glue**

<img width="30" src="/img/resources/aws/analytics/kinesis-data-analytics.png" alt="KinesisDataAnalytics" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.KinesisDataAnalytics**

<img width="30" src="/img/resources/aws/analytics/kinesis-data-firehose.png" alt="KinesisDataFirehose" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.KinesisDataFirehose**

<img width="30" src="/img/resources/aws/analytics/kinesis-data-streams.png" alt="KinesisDataStreams" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.KinesisDataStreams**

<img width="30" src="/img/resources/aws/analytics/kinesis-video-streams.png" alt="KinesisVideoStreams" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.KinesisVideoStreams**

<img width="30" src="/img/resources/aws/analytics/kinesis.png" alt="Kinesis" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.Kinesis**

<img width="30" src="/img/resources/aws/analytics/lake-formation.png" alt="LakeFormation" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.LakeFormation**

<img width="30" src="/img/resources/aws/analytics/managed-streaming-for-kafka.png" alt="ManagedStreamingForKafka" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.ManagedStreamingForKafka**

<img width="30" src="/img/resources/aws/analytics/quicksight.png" alt="Quicksight" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.Quicksight**

<img width="30" src="/img/resources/aws/analytics/redshift-dense-compute-node.png" alt="RedshiftDenseComputeNode" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.RedshiftDenseComputeNode**

<img width="30" src="/img/resources/aws/analytics/redshift-dense-storage-node.png" alt="RedshiftDenseStorageNode" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.RedshiftDenseStorageNode**

<img width="30" src="/img/resources/aws/analytics/redshift.png" alt="Redshift" style="float: left; padding-right: 5px;" >
**diagrams.aws.analytics.Redshift**

## aws.ar


<img width="30" src="/img/resources/aws/ar/ar-vr.png" alt="ArVr" style="float: left; padding-right: 5px;" >
**diagrams.aws.ar.ArVr**

<img width="30" src="/img/resources/aws/ar/sumerian.png" alt="Sumerian" style="float: left; padding-right: 5px;" >
**diagrams.aws.ar.Sumerian**

## aws.blockchain


<img width="30" src="/img/resources/aws/blockchain/blockchain-resource.png" alt="BlockchainResource" style="float: left; padding-right: 5px;" >
**diagrams.aws.blockchain.BlockchainResource**

<img width="30" src="/img/resources/aws/blockchain/blockchain.png" alt="Blockchain" style="float: left; padding-right: 5px;" >
**diagrams.aws.blockchain.Blockchain**

<img width="30" src="/img/resources/aws/blockchain/managed-blockchain.png" alt="ManagedBlockchain" style="float: left; padding-right: 5px;" >
**diagrams.aws.blockchain.ManagedBlockchain**

<img width="30" src="/img/resources/aws/blockchain/quantum-ledger-database-qldb.png" alt="QuantumLedgerDatabaseQldb" style="float: left; padding-right: 5px;" >
**diagrams.aws.blockchain.QuantumLedgerDatabaseQldb**, **QLDB** (alias)

## aws.business


<img width="30" src="/img/resources/aws/business/alexa-for-business.png" alt="AlexaForBusiness" style="float: left; padding-right: 5px;" >
**diagrams.aws.business.AlexaForBusiness**, **A4B** (alias)

<img width="30" src="/img/resources/aws/business/business-applications.png" alt="BusinessApplications" style="float: left; padding-right: 5px;" >
**diagrams.aws.business.BusinessApplications**

<img width="30" src="/img/resources/aws/business/chime.png" alt="Chime" style="float: left; padding-right: 5px;" >
**diagrams.aws.business.Chime**

<img width="30" src="/img/resources/aws/business/workmail.png" alt="Workmail" style="float: left; padding-right: 5px;" >
**diagrams.aws.business.Workmail**

## aws.compute


<img width="30" src="/img/resources/aws/compute/app-runner.png" alt="AppRunner" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.AppRunner**

<img width="30" src="/img/resources/aws/compute/application-auto-scaling.png" alt="ApplicationAutoScaling" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ApplicationAutoScaling**, **AutoScaling** (alias)

<img width="30" src="/img/resources/aws/compute/batch.png" alt="Batch" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.Batch**

<img width="30" src="/img/resources/aws/compute/compute-optimizer.png" alt="ComputeOptimizer" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ComputeOptimizer**

<img width="30" src="/img/resources/aws/compute/compute.png" alt="Compute" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.Compute**

<img width="30" src="/img/resources/aws/compute/ec2-ami.png" alt="EC2Ami" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.EC2Ami**, **AMI** (alias)

<img width="30" src="/img/resources/aws/compute/ec2-auto-scaling.png" alt="EC2AutoScaling" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.EC2AutoScaling**

<img width="30" src="/img/resources/aws/compute/ec2-container-registry-image.png" alt="EC2ContainerRegistryImage" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.EC2ContainerRegistryImage**

<img width="30" src="/img/resources/aws/compute/ec2-container-registry-registry.png" alt="EC2ContainerRegistryRegistry" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.EC2ContainerRegistryRegistry**

<img width="30" src="/img/resources/aws/compute/ec2-container-registry.png" alt="EC2ContainerRegistry" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.EC2ContainerRegistry**, **ECR** (alias)

<img width="30" src="/img/resources/aws/compute/ec2-elastic-ip-address.png" alt="EC2ElasticIpAddress" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.EC2ElasticIpAddress**

<img width="30" src="/img/resources/aws/compute/ec2-image-builder.png" alt="EC2ImageBuilder" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.EC2ImageBuilder**

<img width="30" src="/img/resources/aws/compute/ec2-instance.png" alt="EC2Instance" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.EC2Instance**

<img width="30" src="/img/resources/aws/compute/ec2-instances.png" alt="EC2Instances" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.EC2Instances**

<img width="30" src="/img/resources/aws/compute/ec2-rescue.png" alt="EC2Rescue" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.EC2Rescue**

<img width="30" src="/img/resources/aws/compute/ec2-spot-instance.png" alt="EC2SpotInstance" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.EC2SpotInstance**

<img width="30" src="/img/resources/aws/compute/ec2.png" alt="EC2" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.EC2**

<img width="30" src="/img/resources/aws/compute/elastic-beanstalk-application.png" alt="ElasticBeanstalkApplication" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ElasticBeanstalkApplication**

<img width="30" src="/img/resources/aws/compute/elastic-beanstalk-deployment.png" alt="ElasticBeanstalkDeployment" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ElasticBeanstalkDeployment**

<img width="30" src="/img/resources/aws/compute/elastic-beanstalk.png" alt="ElasticBeanstalk" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ElasticBeanstalk**, **EB** (alias)

<img width="30" src="/img/resources/aws/compute/elastic-container-service-container.png" alt="ElasticContainerServiceContainer" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ElasticContainerServiceContainer**

<img width="30" src="/img/resources/aws/compute/elastic-container-service-service.png" alt="ElasticContainerServiceService" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ElasticContainerServiceService**

<img width="30" src="/img/resources/aws/compute/elastic-container-service-task.png" alt="ElasticContainerServiceTask" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ElasticContainerServiceTask**

<img width="30" src="/img/resources/aws/compute/elastic-container-service.png" alt="ElasticContainerService" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ElasticContainerService**, **ECS** (alias)

<img width="30" src="/img/resources/aws/compute/elastic-kubernetes-service.png" alt="ElasticKubernetesService" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ElasticKubernetesService**, **EKS** (alias)

<img width="30" src="/img/resources/aws/compute/fargate.png" alt="Fargate" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.Fargate**

<img width="30" src="/img/resources/aws/compute/lambda-function.png" alt="LambdaFunction" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.LambdaFunction**

<img width="30" src="/img/resources/aws/compute/lambda.png" alt="Lambda" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.Lambda**

<img width="30" src="/img/resources/aws/compute/lightsail.png" alt="Lightsail" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.Lightsail**

<img width="30" src="/img/resources/aws/compute/local-zones.png" alt="LocalZones" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.LocalZones**

<img width="30" src="/img/resources/aws/compute/outposts.png" alt="Outposts" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.Outposts**

<img width="30" src="/img/resources/aws/compute/serverless-application-repository.png" alt="ServerlessApplicationRepository" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ServerlessApplicationRepository**, **SAR** (alias)

<img width="30" src="/img/resources/aws/compute/thinkbox-deadline.png" alt="ThinkboxDeadline" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ThinkboxDeadline**

<img width="30" src="/img/resources/aws/compute/thinkbox-draft.png" alt="ThinkboxDraft" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ThinkboxDraft**

<img width="30" src="/img/resources/aws/compute/thinkbox-frost.png" alt="ThinkboxFrost" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ThinkboxFrost**

<img width="30" src="/img/resources/aws/compute/thinkbox-krakatoa.png" alt="ThinkboxKrakatoa" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ThinkboxKrakatoa**

<img width="30" src="/img/resources/aws/compute/thinkbox-sequoia.png" alt="ThinkboxSequoia" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ThinkboxSequoia**

<img width="30" src="/img/resources/aws/compute/thinkbox-stoke.png" alt="ThinkboxStoke" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ThinkboxStoke**

<img width="30" src="/img/resources/aws/compute/thinkbox-xmesh.png" alt="ThinkboxXmesh" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.ThinkboxXmesh**

<img width="30" src="/img/resources/aws/compute/vmware-cloud-on-aws.png" alt="VmwareCloudOnAWS" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.VmwareCloudOnAWS**

<img width="30" src="/img/resources/aws/compute/wavelength.png" alt="Wavelength" style="float: left; padding-right: 5px;" >
**diagrams.aws.compute.Wavelength**

## aws.cost


<img width="30" src="/img/resources/aws/cost/budgets.png" alt="Budgets" style="float: left; padding-right: 5px;" >
**diagrams.aws.cost.Budgets**

<img width="30" src="/img/resources/aws/cost/cost-and-usage-report.png" alt="CostAndUsageReport" style="float: left; padding-right: 5px;" >
**diagrams.aws.cost.CostAndUsageReport**

<img width="30" src="/img/resources/aws/cost/cost-explorer.png" alt="CostExplorer" style="float: left; padding-right: 5px;" >
**diagrams.aws.cost.CostExplorer**

<img width="30" src="/img/resources/aws/cost/cost-management.png" alt="CostManagement" style="float: left; padding-right: 5px;" >
**diagrams.aws.cost.CostManagement**

<img width="30" src="/img/resources/aws/cost/reserved-instance-reporting.png" alt="ReservedInstanceReporting" style="float: left; padding-right: 5px;" >
**diagrams.aws.cost.ReservedInstanceReporting**

<img width="30" src="/img/resources/aws/cost/savings-plans.png" alt="SavingsPlans" style="float: left; padding-right: 5px;" >
**diagrams.aws.cost.SavingsPlans**

## aws.database


<img width="30" src="/img/resources/aws/database/aurora-instance.png" alt="AuroraInstance" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.AuroraInstance**

<img width="30" src="/img/resources/aws/database/aurora.png" alt="Aurora" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.Aurora**

<img width="30" src="/img/resources/aws/database/database-migration-service-database-migration-workflow.png" alt="DatabaseMigrationServiceDatabaseMigrationWorkflow" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.DatabaseMigrationServiceDatabaseMigrationWorkflow**

<img width="30" src="/img/resources/aws/database/database-migration-service.png" alt="DatabaseMigrationService" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.DatabaseMigrationService**, **DMS** (alias)

<img width="30" src="/img/resources/aws/database/database.png" alt="Database" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.Database**, **DB** (alias)

<img width="30" src="/img/resources/aws/database/documentdb-mongodb-compatibility.png" alt="DocumentdbMongodbCompatibility" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.DocumentdbMongodbCompatibility**, **DocumentDB** (alias)

<img width="30" src="/img/resources/aws/database/dynamodb-attribute.png" alt="DynamodbAttribute" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.DynamodbAttribute**

<img width="30" src="/img/resources/aws/database/dynamodb-attributes.png" alt="DynamodbAttributes" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.DynamodbAttributes**

<img width="30" src="/img/resources/aws/database/dynamodb-dax.png" alt="DynamodbDax" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.DynamodbDax**, **DAX** (alias)

<img width="30" src="/img/resources/aws/database/dynamodb-global-secondary-index.png" alt="DynamodbGlobalSecondaryIndex" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.DynamodbGlobalSecondaryIndex**, **DynamodbGSI** (alias)

<img width="30" src="/img/resources/aws/database/dynamodb-item.png" alt="DynamodbItem" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.DynamodbItem**

<img width="30" src="/img/resources/aws/database/dynamodb-items.png" alt="DynamodbItems" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.DynamodbItems**

<img width="30" src="/img/resources/aws/database/dynamodb-streams.png" alt="DynamodbStreams" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.DynamodbStreams**

<img width="30" src="/img/resources/aws/database/dynamodb-table.png" alt="DynamodbTable" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.DynamodbTable**

<img width="30" src="/img/resources/aws/database/dynamodb.png" alt="Dynamodb" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.Dynamodb**, **DDB** (alias)

<img width="30" src="/img/resources/aws/database/elasticache-cache-node.png" alt="ElasticacheCacheNode" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.ElasticacheCacheNode**

<img width="30" src="/img/resources/aws/database/elasticache-for-memcached.png" alt="ElasticacheForMemcached" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.ElasticacheForMemcached**

<img width="30" src="/img/resources/aws/database/elasticache-for-redis.png" alt="ElasticacheForRedis" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.ElasticacheForRedis**

<img width="30" src="/img/resources/aws/database/elasticache.png" alt="Elasticache" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.Elasticache**, **ElastiCache** (alias)

<img width="30" src="/img/resources/aws/database/keyspaces-managed-apache-cassandra-service.png" alt="KeyspacesManagedApacheCassandraService" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.KeyspacesManagedApacheCassandraService**

<img width="30" src="/img/resources/aws/database/neptune.png" alt="Neptune" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.Neptune**

<img width="30" src="/img/resources/aws/database/quantum-ledger-database-qldb.png" alt="QuantumLedgerDatabaseQldb" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.QuantumLedgerDatabaseQldb**, **QLDB** (alias)

<img width="30" src="/img/resources/aws/database/rds-instance.png" alt="RDSInstance" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.RDSInstance**

<img width="30" src="/img/resources/aws/database/rds-mariadb-instance.png" alt="RDSMariadbInstance" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.RDSMariadbInstance**

<img width="30" src="/img/resources/aws/database/rds-mysql-instance.png" alt="RDSMysqlInstance" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.RDSMysqlInstance**

<img width="30" src="/img/resources/aws/database/rds-on-vmware.png" alt="RDSOnVmware" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.RDSOnVmware**

<img width="30" src="/img/resources/aws/database/rds-oracle-instance.png" alt="RDSOracleInstance" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.RDSOracleInstance**

<img width="30" src="/img/resources/aws/database/rds-postgresql-instance.png" alt="RDSPostgresqlInstance" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.RDSPostgresqlInstance**

<img width="30" src="/img/resources/aws/database/rds-sql-server-instance.png" alt="RDSSqlServerInstance" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.RDSSqlServerInstance**

<img width="30" src="/img/resources/aws/database/rds.png" alt="RDS" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.RDS**

<img width="30" src="/img/resources/aws/database/redshift-dense-compute-node.png" alt="RedshiftDenseComputeNode" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.RedshiftDenseComputeNode**

<img width="30" src="/img/resources/aws/database/redshift-dense-storage-node.png" alt="RedshiftDenseStorageNode" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.RedshiftDenseStorageNode**

<img width="30" src="/img/resources/aws/database/redshift.png" alt="Redshift" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.Redshift**

<img width="30" src="/img/resources/aws/database/timestream.png" alt="Timestream" style="float: left; padding-right: 5px;" >
**diagrams.aws.database.Timestream**

## aws.devtools


<img width="30" src="/img/resources/aws/devtools/cloud-development-kit.png" alt="CloudDevelopmentKit" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.CloudDevelopmentKit**

<img width="30" src="/img/resources/aws/devtools/cloud9-resource.png" alt="Cloud9Resource" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.Cloud9Resource**

<img width="30" src="/img/resources/aws/devtools/cloud9.png" alt="Cloud9" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.Cloud9**

<img width="30" src="/img/resources/aws/devtools/cloudshell.png" alt="Cloudshell" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.Cloudshell**

<img width="30" src="/img/resources/aws/devtools/codeartifact.png" alt="Codeartifact" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.Codeartifact**

<img width="30" src="/img/resources/aws/devtools/codebuild.png" alt="Codebuild" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.Codebuild**

<img width="30" src="/img/resources/aws/devtools/codecommit.png" alt="Codecommit" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.Codecommit**

<img width="30" src="/img/resources/aws/devtools/codedeploy.png" alt="Codedeploy" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.Codedeploy**

<img width="30" src="/img/resources/aws/devtools/codepipeline.png" alt="Codepipeline" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.Codepipeline**

<img width="30" src="/img/resources/aws/devtools/codestar.png" alt="Codestar" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.Codestar**

<img width="30" src="/img/resources/aws/devtools/command-line-interface.png" alt="CommandLineInterface" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.CommandLineInterface**, **CLI** (alias)

<img width="30" src="/img/resources/aws/devtools/developer-tools.png" alt="DeveloperTools" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.DeveloperTools**, **DevTools** (alias)

<img width="30" src="/img/resources/aws/devtools/tools-and-sdks.png" alt="ToolsAndSdks" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.ToolsAndSdks**

<img width="30" src="/img/resources/aws/devtools/x-ray.png" alt="XRay" style="float: left; padding-right: 5px;" >
**diagrams.aws.devtools.XRay**

## aws.enablement


<img width="30" src="/img/resources/aws/enablement/customer-enablement.png" alt="CustomerEnablement" style="float: left; padding-right: 5px;" >
**diagrams.aws.enablement.CustomerEnablement**

<img width="30" src="/img/resources/aws/enablement/iq.png" alt="Iq" style="float: left; padding-right: 5px;" >
**diagrams.aws.enablement.Iq**

<img width="30" src="/img/resources/aws/enablement/managed-services.png" alt="ManagedServices" style="float: left; padding-right: 5px;" >
**diagrams.aws.enablement.ManagedServices**

<img width="30" src="/img/resources/aws/enablement/professional-services.png" alt="ProfessionalServices" style="float: left; padding-right: 5px;" >
**diagrams.aws.enablement.ProfessionalServices**

<img width="30" src="/img/resources/aws/enablement/support.png" alt="Support" style="float: left; padding-right: 5px;" >
**diagrams.aws.enablement.Support**

## aws.enduser


<img width="30" src="/img/resources/aws/enduser/appstream-2-0.png" alt="Appstream20" style="float: left; padding-right: 5px;" >
**diagrams.aws.enduser.Appstream20**

<img width="30" src="/img/resources/aws/enduser/desktop-and-app-streaming.png" alt="DesktopAndAppStreaming" style="float: left; padding-right: 5px;" >
**diagrams.aws.enduser.DesktopAndAppStreaming**

<img width="30" src="/img/resources/aws/enduser/workdocs.png" alt="Workdocs" style="float: left; padding-right: 5px;" >
**diagrams.aws.enduser.Workdocs**

<img width="30" src="/img/resources/aws/enduser/worklink.png" alt="Worklink" style="float: left; padding-right: 5px;" >
**diagrams.aws.enduser.Worklink**

<img width="30" src="/img/resources/aws/enduser/workspaces.png" alt="Workspaces" style="float: left; padding-right: 5px;" >
**diagrams.aws.enduser.Workspaces**

## aws.engagement


<img width="30" src="/img/resources/aws/engagement/connect.png" alt="Connect" style="float: left; padding-right: 5px;" >
**diagrams.aws.engagement.Connect**

<img width="30" src="/img/resources/aws/engagement/customer-engagement.png" alt="CustomerEngagement" style="float: left; padding-right: 5px;" >
**diagrams.aws.engagement.CustomerEngagement**

<img width="30" src="/img/resources/aws/engagement/pinpoint.png" alt="Pinpoint" style="float: left; padding-right: 5px;" >
**diagrams.aws.engagement.Pinpoint**

<img width="30" src="/img/resources/aws/engagement/simple-email-service-ses-email.png" alt="SimpleEmailServiceSesEmail" style="float: left; padding-right: 5px;" >
**diagrams.aws.engagement.SimpleEmailServiceSesEmail**

<img width="30" src="/img/resources/aws/engagement/simple-email-service-ses.png" alt="SimpleEmailServiceSes" style="float: left; padding-right: 5px;" >
**diagrams.aws.engagement.SimpleEmailServiceSes**, **SES** (alias)

## aws.game


<img width="30" src="/img/resources/aws/game/game-tech.png" alt="GameTech" style="float: left; padding-right: 5px;" >
**diagrams.aws.game.GameTech**

<img width="30" src="/img/resources/aws/game/gamelift.png" alt="Gamelift" style="float: left; padding-right: 5px;" >
**diagrams.aws.game.Gamelift**

## aws.general


<img width="30" src="/img/resources/aws/general/client.png" alt="Client" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.Client**

<img width="30" src="/img/resources/aws/general/disk.png" alt="Disk" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.Disk**

<img width="30" src="/img/resources/aws/general/forums.png" alt="Forums" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.Forums**

<img width="30" src="/img/resources/aws/general/general.png" alt="General" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.General**

<img width="30" src="/img/resources/aws/general/generic-database.png" alt="GenericDatabase" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.GenericDatabase**

<img width="30" src="/img/resources/aws/general/generic-firewall.png" alt="GenericFirewall" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.GenericFirewall**

<img width="30" src="/img/resources/aws/general/generic-office-building.png" alt="GenericOfficeBuilding" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.GenericOfficeBuilding**, **OfficeBuilding** (alias)

<img width="30" src="/img/resources/aws/general/generic-saml-token.png" alt="GenericSamlToken" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.GenericSamlToken**

<img width="30" src="/img/resources/aws/general/generic-sdk.png" alt="GenericSDK" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.GenericSDK**

<img width="30" src="/img/resources/aws/general/internet-alt1.png" alt="InternetAlt1" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.InternetAlt1**

<img width="30" src="/img/resources/aws/general/internet-alt2.png" alt="InternetAlt2" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.InternetAlt2**

<img width="30" src="/img/resources/aws/general/internet-gateway.png" alt="InternetGateway" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.InternetGateway**

<img width="30" src="/img/resources/aws/general/marketplace.png" alt="Marketplace" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.Marketplace**

<img width="30" src="/img/resources/aws/general/mobile-client.png" alt="MobileClient" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.MobileClient**

<img width="30" src="/img/resources/aws/general/multimedia.png" alt="Multimedia" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.Multimedia**

<img width="30" src="/img/resources/aws/general/office-building.png" alt="OfficeBuilding" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.OfficeBuilding**

<img width="30" src="/img/resources/aws/general/saml-token.png" alt="SamlToken" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.SamlToken**

<img width="30" src="/img/resources/aws/general/sdk.png" alt="SDK" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.SDK**

<img width="30" src="/img/resources/aws/general/ssl-padlock.png" alt="SslPadlock" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.SslPadlock**

<img width="30" src="/img/resources/aws/general/tape-storage.png" alt="TapeStorage" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.TapeStorage**

<img width="30" src="/img/resources/aws/general/toolkit.png" alt="Toolkit" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.Toolkit**

<img width="30" src="/img/resources/aws/general/traditional-server.png" alt="TraditionalServer" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.TraditionalServer**

<img width="30" src="/img/resources/aws/general/user.png" alt="User" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.User**

<img width="30" src="/img/resources/aws/general/users.png" alt="Users" style="float: left; padding-right: 5px;" >
**diagrams.aws.general.Users**

## aws.integration


<img width="30" src="/img/resources/aws/integration/application-integration.png" alt="ApplicationIntegration" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.ApplicationIntegration**

<img width="30" src="/img/resources/aws/integration/appsync.png" alt="Appsync" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.Appsync**

<img width="30" src="/img/resources/aws/integration/console-mobile-application.png" alt="ConsoleMobileApplication" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.ConsoleMobileApplication**

<img width="30" src="/img/resources/aws/integration/event-resource.png" alt="EventResource" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.EventResource**

<img width="30" src="/img/resources/aws/integration/eventbridge-custom-event-bus-resource.png" alt="EventbridgeCustomEventBusResource" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.EventbridgeCustomEventBusResource**

<img width="30" src="/img/resources/aws/integration/eventbridge-default-event-bus-resource.png" alt="EventbridgeDefaultEventBusResource" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.EventbridgeDefaultEventBusResource**

<img width="30" src="/img/resources/aws/integration/eventbridge-event.png" alt="EventbridgeEvent" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.EventbridgeEvent**

<img width="30" src="/img/resources/aws/integration/eventbridge-pipes.png" alt="EventbridgePipes" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.EventbridgePipes**

<img width="30" src="/img/resources/aws/integration/eventbridge-rule.png" alt="EventbridgeRule" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.EventbridgeRule**

<img width="30" src="/img/resources/aws/integration/eventbridge-saas-partner-event-bus-resource.png" alt="EventbridgeSaasPartnerEventBusResource" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.EventbridgeSaasPartnerEventBusResource**

<img width="30" src="/img/resources/aws/integration/eventbridge-scheduler.png" alt="EventbridgeScheduler" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.EventbridgeScheduler**

<img width="30" src="/img/resources/aws/integration/eventbridge-schema.png" alt="EventbridgeSchema" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.EventbridgeSchema**

<img width="30" src="/img/resources/aws/integration/eventbridge.png" alt="Eventbridge" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.Eventbridge**

<img width="30" src="/img/resources/aws/integration/express-workflows.png" alt="ExpressWorkflows" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.ExpressWorkflows**

<img width="30" src="/img/resources/aws/integration/mq.png" alt="MQ" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.MQ**

<img width="30" src="/img/resources/aws/integration/simple-notification-service-sns-email-notification.png" alt="SimpleNotificationServiceSnsEmailNotification" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.SimpleNotificationServiceSnsEmailNotification**

<img width="30" src="/img/resources/aws/integration/simple-notification-service-sns-http-notification.png" alt="SimpleNotificationServiceSnsHttpNotification" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.SimpleNotificationServiceSnsHttpNotification**

<img width="30" src="/img/resources/aws/integration/simple-notification-service-sns-topic.png" alt="SimpleNotificationServiceSnsTopic" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.SimpleNotificationServiceSnsTopic**

<img width="30" src="/img/resources/aws/integration/simple-notification-service-sns.png" alt="SimpleNotificationServiceSns" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.SimpleNotificationServiceSns**, **SNS** (alias)

<img width="30" src="/img/resources/aws/integration/simple-queue-service-sqs-message.png" alt="SimpleQueueServiceSqsMessage" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.SimpleQueueServiceSqsMessage**

<img width="30" src="/img/resources/aws/integration/simple-queue-service-sqs-queue.png" alt="SimpleQueueServiceSqsQueue" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.SimpleQueueServiceSqsQueue**

<img width="30" src="/img/resources/aws/integration/simple-queue-service-sqs.png" alt="SimpleQueueServiceSqs" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.SimpleQueueServiceSqs**, **SQS** (alias)

<img width="30" src="/img/resources/aws/integration/step-functions.png" alt="StepFunctions" style="float: left; padding-right: 5px;" >
**diagrams.aws.integration.StepFunctions**, **SF** (alias)

## aws.iot


<img width="30" src="/img/resources/aws/iot/freertos.png" alt="Freertos" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.Freertos**, **FreeRTOS** (alias)

<img width="30" src="/img/resources/aws/iot/internet-of-things.png" alt="InternetOfThings" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.InternetOfThings**

<img width="30" src="/img/resources/aws/iot/iot-1-click.png" alt="Iot1Click" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.Iot1Click**

<img width="30" src="/img/resources/aws/iot/iot-action.png" alt="IotAction" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotAction**

<img width="30" src="/img/resources/aws/iot/iot-actuator.png" alt="IotActuator" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotActuator**

<img width="30" src="/img/resources/aws/iot/iot-alexa-echo.png" alt="IotAlexaEcho" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotAlexaEcho**

<img width="30" src="/img/resources/aws/iot/iot-alexa-enabled-device.png" alt="IotAlexaEnabledDevice" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotAlexaEnabledDevice**

<img width="30" src="/img/resources/aws/iot/iot-alexa-skill.png" alt="IotAlexaSkill" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotAlexaSkill**

<img width="30" src="/img/resources/aws/iot/iot-alexa-voice-service.png" alt="IotAlexaVoiceService" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotAlexaVoiceService**

<img width="30" src="/img/resources/aws/iot/iot-analytics-channel.png" alt="IotAnalyticsChannel" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotAnalyticsChannel**

<img width="30" src="/img/resources/aws/iot/iot-analytics-data-set.png" alt="IotAnalyticsDataSet" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotAnalyticsDataSet**

<img width="30" src="/img/resources/aws/iot/iot-analytics-data-store.png" alt="IotAnalyticsDataStore" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotAnalyticsDataStore**

<img width="30" src="/img/resources/aws/iot/iot-analytics-notebook.png" alt="IotAnalyticsNotebook" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotAnalyticsNotebook**

<img width="30" src="/img/resources/aws/iot/iot-analytics-pipeline.png" alt="IotAnalyticsPipeline" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotAnalyticsPipeline**

<img width="30" src="/img/resources/aws/iot/iot-analytics.png" alt="IotAnalytics" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotAnalytics**

<img width="30" src="/img/resources/aws/iot/iot-bank.png" alt="IotBank" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotBank**

<img width="30" src="/img/resources/aws/iot/iot-bicycle.png" alt="IotBicycle" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotBicycle**

<img width="30" src="/img/resources/aws/iot/iot-button.png" alt="IotButton" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotButton**

<img width="30" src="/img/resources/aws/iot/iot-camera.png" alt="IotCamera" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotCamera**

<img width="30" src="/img/resources/aws/iot/iot-car.png" alt="IotCar" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotCar**

<img width="30" src="/img/resources/aws/iot/iot-cart.png" alt="IotCart" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotCart**

<img width="30" src="/img/resources/aws/iot/iot-certificate.png" alt="IotCertificate" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotCertificate**

<img width="30" src="/img/resources/aws/iot/iot-coffee-pot.png" alt="IotCoffeePot" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotCoffeePot**

<img width="30" src="/img/resources/aws/iot/iot-core.png" alt="IotCore" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotCore**

<img width="30" src="/img/resources/aws/iot/iot-desired-state.png" alt="IotDesiredState" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotDesiredState**

<img width="30" src="/img/resources/aws/iot/iot-device-defender.png" alt="IotDeviceDefender" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotDeviceDefender**

<img width="30" src="/img/resources/aws/iot/iot-device-gateway.png" alt="IotDeviceGateway" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotDeviceGateway**

<img width="30" src="/img/resources/aws/iot/iot-device-management.png" alt="IotDeviceManagement" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotDeviceManagement**

<img width="30" src="/img/resources/aws/iot/iot-door-lock.png" alt="IotDoorLock" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotDoorLock**

<img width="30" src="/img/resources/aws/iot/iot-events.png" alt="IotEvents" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotEvents**

<img width="30" src="/img/resources/aws/iot/iot-factory.png" alt="IotFactory" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotFactory**

<img width="30" src="/img/resources/aws/iot/iot-fire-tv-stick.png" alt="IotFireTvStick" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotFireTvStick**

<img width="30" src="/img/resources/aws/iot/iot-fire-tv.png" alt="IotFireTv" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotFireTv**

<img width="30" src="/img/resources/aws/iot/iot-generic.png" alt="IotGeneric" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotGeneric**

<img width="30" src="/img/resources/aws/iot/iot-greengrass-connector.png" alt="IotGreengrassConnector" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotGreengrassConnector**

<img width="30" src="/img/resources/aws/iot/iot-greengrass.png" alt="IotGreengrass" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotGreengrass**

<img width="30" src="/img/resources/aws/iot/iot-hardware-board.png" alt="IotHardwareBoard" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotHardwareBoard**, **IotBoard** (alias)

<img width="30" src="/img/resources/aws/iot/iot-house.png" alt="IotHouse" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotHouse**

<img width="30" src="/img/resources/aws/iot/iot-http.png" alt="IotHttp" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotHttp**

<img width="30" src="/img/resources/aws/iot/iot-http2.png" alt="IotHttp2" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotHttp2**

<img width="30" src="/img/resources/aws/iot/iot-jobs.png" alt="IotJobs" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotJobs**

<img width="30" src="/img/resources/aws/iot/iot-lambda.png" alt="IotLambda" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotLambda**

<img width="30" src="/img/resources/aws/iot/iot-lightbulb.png" alt="IotLightbulb" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotLightbulb**

<img width="30" src="/img/resources/aws/iot/iot-medical-emergency.png" alt="IotMedicalEmergency" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotMedicalEmergency**

<img width="30" src="/img/resources/aws/iot/iot-mqtt.png" alt="IotMqtt" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotMqtt**

<img width="30" src="/img/resources/aws/iot/iot-over-the-air-update.png" alt="IotOverTheAirUpdate" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotOverTheAirUpdate**

<img width="30" src="/img/resources/aws/iot/iot-policy-emergency.png" alt="IotPolicyEmergency" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotPolicyEmergency**

<img width="30" src="/img/resources/aws/iot/iot-policy.png" alt="IotPolicy" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotPolicy**

<img width="30" src="/img/resources/aws/iot/iot-reported-state.png" alt="IotReportedState" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotReportedState**

<img width="30" src="/img/resources/aws/iot/iot-rule.png" alt="IotRule" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotRule**

<img width="30" src="/img/resources/aws/iot/iot-sensor.png" alt="IotSensor" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotSensor**

<img width="30" src="/img/resources/aws/iot/iot-servo.png" alt="IotServo" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotServo**

<img width="30" src="/img/resources/aws/iot/iot-shadow.png" alt="IotShadow" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotShadow**

<img width="30" src="/img/resources/aws/iot/iot-simulator.png" alt="IotSimulator" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotSimulator**

<img width="30" src="/img/resources/aws/iot/iot-sitewise.png" alt="IotSitewise" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotSitewise**

<img width="30" src="/img/resources/aws/iot/iot-thermostat.png" alt="IotThermostat" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotThermostat**

<img width="30" src="/img/resources/aws/iot/iot-things-graph.png" alt="IotThingsGraph" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotThingsGraph**

<img width="30" src="/img/resources/aws/iot/iot-topic.png" alt="IotTopic" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotTopic**

<img width="30" src="/img/resources/aws/iot/iot-travel.png" alt="IotTravel" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotTravel**

<img width="30" src="/img/resources/aws/iot/iot-utility.png" alt="IotUtility" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotUtility**

<img width="30" src="/img/resources/aws/iot/iot-windfarm.png" alt="IotWindfarm" style="float: left; padding-right: 5px;" >
**diagrams.aws.iot.IotWindfarm**

## aws.management


<img width="30" src="/img/resources/aws/management/amazon-devops-guru.png" alt="AmazonDevopsGuru" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.AmazonDevopsGuru**

<img width="30" src="/img/resources/aws/management/amazon-managed-grafana.png" alt="AmazonManagedGrafana" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.AmazonManagedGrafana**

<img width="30" src="/img/resources/aws/management/amazon-managed-prometheus.png" alt="AmazonManagedPrometheus" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.AmazonManagedPrometheus**

<img width="30" src="/img/resources/aws/management/amazon-managed-workflows-apache-airflow.png" alt="AmazonManagedWorkflowsApacheAirflow" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.AmazonManagedWorkflowsApacheAirflow**

<img width="30" src="/img/resources/aws/management/auto-scaling.png" alt="AutoScaling" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.AutoScaling**

<img width="30" src="/img/resources/aws/management/chatbot.png" alt="Chatbot" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.Chatbot**

<img width="30" src="/img/resources/aws/management/cloudformation-change-set.png" alt="CloudformationChangeSet" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.CloudformationChangeSet**

<img width="30" src="/img/resources/aws/management/cloudformation-stack.png" alt="CloudformationStack" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.CloudformationStack**

<img width="30" src="/img/resources/aws/management/cloudformation-template.png" alt="CloudformationTemplate" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.CloudformationTemplate**

<img width="30" src="/img/resources/aws/management/cloudformation.png" alt="Cloudformation" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.Cloudformation**

<img width="30" src="/img/resources/aws/management/cloudtrail.png" alt="Cloudtrail" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.Cloudtrail**

<img width="30" src="/img/resources/aws/management/cloudwatch-alarm.png" alt="CloudwatchAlarm" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.CloudwatchAlarm**

<img width="30" src="/img/resources/aws/management/cloudwatch-event-event-based.png" alt="CloudwatchEventEventBased" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.CloudwatchEventEventBased**

<img width="30" src="/img/resources/aws/management/cloudwatch-event-time-based.png" alt="CloudwatchEventTimeBased" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.CloudwatchEventTimeBased**

<img width="30" src="/img/resources/aws/management/cloudwatch-logs.png" alt="CloudwatchLogs" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.CloudwatchLogs**

<img width="30" src="/img/resources/aws/management/cloudwatch-rule.png" alt="CloudwatchRule" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.CloudwatchRule**

<img width="30" src="/img/resources/aws/management/cloudwatch.png" alt="Cloudwatch" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.Cloudwatch**

<img width="30" src="/img/resources/aws/management/codeguru.png" alt="Codeguru" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.Codeguru**

<img width="30" src="/img/resources/aws/management/command-line-interface.png" alt="CommandLineInterface" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.CommandLineInterface**

<img width="30" src="/img/resources/aws/management/config.png" alt="Config" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.Config**

<img width="30" src="/img/resources/aws/management/control-tower.png" alt="ControlTower" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.ControlTower**

<img width="30" src="/img/resources/aws/management/license-manager.png" alt="LicenseManager" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.LicenseManager**

<img width="30" src="/img/resources/aws/management/managed-services.png" alt="ManagedServices" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.ManagedServices**

<img width="30" src="/img/resources/aws/management/management-and-governance.png" alt="ManagementAndGovernance" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.ManagementAndGovernance**

<img width="30" src="/img/resources/aws/management/management-console.png" alt="ManagementConsole" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.ManagementConsole**

<img width="30" src="/img/resources/aws/management/opsworks-apps.png" alt="OpsworksApps" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.OpsworksApps**

<img width="30" src="/img/resources/aws/management/opsworks-deployments.png" alt="OpsworksDeployments" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.OpsworksDeployments**

<img width="30" src="/img/resources/aws/management/opsworks-instances.png" alt="OpsworksInstances" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.OpsworksInstances**

<img width="30" src="/img/resources/aws/management/opsworks-layers.png" alt="OpsworksLayers" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.OpsworksLayers**

<img width="30" src="/img/resources/aws/management/opsworks-monitoring.png" alt="OpsworksMonitoring" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.OpsworksMonitoring**

<img width="30" src="/img/resources/aws/management/opsworks-permissions.png" alt="OpsworksPermissions" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.OpsworksPermissions**

<img width="30" src="/img/resources/aws/management/opsworks-resources.png" alt="OpsworksResources" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.OpsworksResources**

<img width="30" src="/img/resources/aws/management/opsworks-stack.png" alt="OpsworksStack" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.OpsworksStack**

<img width="30" src="/img/resources/aws/management/opsworks.png" alt="Opsworks" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.Opsworks**

<img width="30" src="/img/resources/aws/management/organizations-account.png" alt="OrganizationsAccount" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.OrganizationsAccount**

<img width="30" src="/img/resources/aws/management/organizations-organizational-unit.png" alt="OrganizationsOrganizationalUnit" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.OrganizationsOrganizationalUnit**

<img width="30" src="/img/resources/aws/management/organizations.png" alt="Organizations" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.Organizations**

<img width="30" src="/img/resources/aws/management/personal-health-dashboard.png" alt="PersonalHealthDashboard" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.PersonalHealthDashboard**

<img width="30" src="/img/resources/aws/management/proton.png" alt="Proton" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.Proton**

<img width="30" src="/img/resources/aws/management/service-catalog.png" alt="ServiceCatalog" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.ServiceCatalog**

<img width="30" src="/img/resources/aws/management/systems-manager-app-config.png" alt="SystemsManagerAppConfig" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.SystemsManagerAppConfig**

<img width="30" src="/img/resources/aws/management/systems-manager-automation.png" alt="SystemsManagerAutomation" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.SystemsManagerAutomation**

<img width="30" src="/img/resources/aws/management/systems-manager-documents.png" alt="SystemsManagerDocuments" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.SystemsManagerDocuments**

<img width="30" src="/img/resources/aws/management/systems-manager-inventory.png" alt="SystemsManagerInventory" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.SystemsManagerInventory**

<img width="30" src="/img/resources/aws/management/systems-manager-maintenance-windows.png" alt="SystemsManagerMaintenanceWindows" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.SystemsManagerMaintenanceWindows**

<img width="30" src="/img/resources/aws/management/systems-manager-opscenter.png" alt="SystemsManagerOpscenter" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.SystemsManagerOpscenter**

<img width="30" src="/img/resources/aws/management/systems-manager-parameter-store.png" alt="SystemsManagerParameterStore" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.SystemsManagerParameterStore**, **ParameterStore** (alias)

<img width="30" src="/img/resources/aws/management/systems-manager-patch-manager.png" alt="SystemsManagerPatchManager" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.SystemsManagerPatchManager**

<img width="30" src="/img/resources/aws/management/systems-manager-run-command.png" alt="SystemsManagerRunCommand" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.SystemsManagerRunCommand**

<img width="30" src="/img/resources/aws/management/systems-manager-state-manager.png" alt="SystemsManagerStateManager" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.SystemsManagerStateManager**

<img width="30" src="/img/resources/aws/management/systems-manager.png" alt="SystemsManager" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.SystemsManager**, **SSM** (alias)

<img width="30" src="/img/resources/aws/management/trusted-advisor-checklist-cost.png" alt="TrustedAdvisorChecklistCost" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.TrustedAdvisorChecklistCost**

<img width="30" src="/img/resources/aws/management/trusted-advisor-checklist-fault-tolerant.png" alt="TrustedAdvisorChecklistFaultTolerant" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.TrustedAdvisorChecklistFaultTolerant**

<img width="30" src="/img/resources/aws/management/trusted-advisor-checklist-performance.png" alt="TrustedAdvisorChecklistPerformance" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.TrustedAdvisorChecklistPerformance**

<img width="30" src="/img/resources/aws/management/trusted-advisor-checklist-security.png" alt="TrustedAdvisorChecklistSecurity" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.TrustedAdvisorChecklistSecurity**

<img width="30" src="/img/resources/aws/management/trusted-advisor-checklist.png" alt="TrustedAdvisorChecklist" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.TrustedAdvisorChecklist**

<img width="30" src="/img/resources/aws/management/trusted-advisor.png" alt="TrustedAdvisor" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.TrustedAdvisor**

<img width="30" src="/img/resources/aws/management/user-notifications.png" alt="UserNotifications" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.UserNotifications**

<img width="30" src="/img/resources/aws/management/well-architected-tool.png" alt="WellArchitectedTool" style="float: left; padding-right: 5px;" >
**diagrams.aws.management.WellArchitectedTool**

## aws.media


<img width="30" src="/img/resources/aws/media/elastic-transcoder.png" alt="ElasticTranscoder" style="float: left; padding-right: 5px;" >
**diagrams.aws.media.ElasticTranscoder**

<img width="30" src="/img/resources/aws/media/elemental-conductor.png" alt="ElementalConductor" style="float: left; padding-right: 5px;" >
**diagrams.aws.media.ElementalConductor**

<img width="30" src="/img/resources/aws/media/elemental-delta.png" alt="ElementalDelta" style="float: left; padding-right: 5px;" >
**diagrams.aws.media.ElementalDelta**

<img width="30" src="/img/resources/aws/media/elemental-live.png" alt="ElementalLive" style="float: left; padding-right: 5px;" >
**diagrams.aws.media.ElementalLive**

<img width="30" src="/img/resources/aws/media/elemental-mediaconnect.png" alt="ElementalMediaconnect" style="float: left; padding-right: 5px;" >
**diagrams.aws.media.ElementalMediaconnect**

<img width="30" src="/img/resources/aws/media/elemental-mediaconvert.png" alt="ElementalMediaconvert" style="float: left; padding-right: 5px;" >
**diagrams.aws.media.ElementalMediaconvert**

<img width="30" src="/img/resources/aws/media/elemental-medialive.png" alt="ElementalMedialive" style="float: left; padding-right: 5px;" >
**diagrams.aws.media.ElementalMedialive**

<img width="30" src="/img/resources/aws/media/elemental-mediapackage.png" alt="ElementalMediapackage" style="float: left; padding-right: 5px;" >
**diagrams.aws.media.ElementalMediapackage**

<img width="30" src="/img/resources/aws/media/elemental-mediastore.png" alt="ElementalMediastore" style="float: left; padding-right: 5px;" >
**diagrams.aws.media.ElementalMediastore**

<img width="30" src="/img/resources/aws/media/elemental-mediatailor.png" alt="ElementalMediatailor" style="float: left; padding-right: 5px;" >
**diagrams.aws.media.ElementalMediatailor**

<img width="30" src="/img/resources/aws/media/elemental-server.png" alt="ElementalServer" style="float: left; padding-right: 5px;" >
**diagrams.aws.media.ElementalServer**

<img width="30" src="/img/resources/aws/media/kinesis-video-streams.png" alt="KinesisVideoStreams" style="float: left; padding-right: 5px;" >
**diagrams.aws.media.KinesisVideoStreams**

<img width="30" src="/img/resources/aws/media/media-services.png" alt="MediaServices" style="float: left; padding-right: 5px;" >
**diagrams.aws.media.MediaServices**

## aws.migration


<img width="30" src="/img/resources/aws/migration/application-discovery-service.png" alt="ApplicationDiscoveryService" style="float: left; padding-right: 5px;" >
**diagrams.aws.migration.ApplicationDiscoveryService**, **ADS** (alias)

<img width="30" src="/img/resources/aws/migration/cloudendure-migration.png" alt="CloudendureMigration" style="float: left; padding-right: 5px;" >
**diagrams.aws.migration.CloudendureMigration**, **CEM** (alias)

<img width="30" src="/img/resources/aws/migration/database-migration-service.png" alt="DatabaseMigrationService" style="float: left; padding-right: 5px;" >
**diagrams.aws.migration.DatabaseMigrationService**, **DMS** (alias)

<img width="30" src="/img/resources/aws/migration/datasync-agent.png" alt="DatasyncAgent" style="float: left; padding-right: 5px;" >
**diagrams.aws.migration.DatasyncAgent**

<img width="30" src="/img/resources/aws/migration/datasync.png" alt="Datasync" style="float: left; padding-right: 5px;" >
**diagrams.aws.migration.Datasync**

<img width="30" src="/img/resources/aws/migration/migration-and-transfer.png" alt="MigrationAndTransfer" style="float: left; padding-right: 5px;" >
**diagrams.aws.migration.MigrationAndTransfer**, **MAT** (alias)

<img width="30" src="/img/resources/aws/migration/migration-hub.png" alt="MigrationHub" style="float: left; padding-right: 5px;" >
**diagrams.aws.migration.MigrationHub**

<img width="30" src="/img/resources/aws/migration/server-migration-service.png" alt="ServerMigrationService" style="float: left; padding-right: 5px;" >
**diagrams.aws.migration.ServerMigrationService**, **SMS** (alias)

<img width="30" src="/img/resources/aws/migration/snowball-edge.png" alt="SnowballEdge" style="float: left; padding-right: 5px;" >
**diagrams.aws.migration.SnowballEdge**

<img width="30" src="/img/resources/aws/migration/snowball.png" alt="Snowball" style="float: left; padding-right: 5px;" >
**diagrams.aws.migration.Snowball**

<img width="30" src="/img/resources/aws/migration/snowmobile.png" alt="Snowmobile" style="float: left; padding-right: 5px;" >
**diagrams.aws.migration.Snowmobile**

<img width="30" src="/img/resources/aws/migration/transfer-for-sftp.png" alt="TransferForSftp" style="float: left; padding-right: 5px;" >
**diagrams.aws.migration.TransferForSftp**

## aws.ml


<img width="30" src="/img/resources/aws/ml/apache-mxnet-on-aws.png" alt="ApacheMxnetOnAWS" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.ApacheMxnetOnAWS**

<img width="30" src="/img/resources/aws/ml/augmented-ai.png" alt="AugmentedAi" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.AugmentedAi**

<img width="30" src="/img/resources/aws/ml/bedrock.png" alt="Bedrock" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Bedrock**

<img width="30" src="/img/resources/aws/ml/comprehend.png" alt="Comprehend" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Comprehend**

<img width="30" src="/img/resources/aws/ml/deep-learning-amis.png" alt="DeepLearningAmis" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.DeepLearningAmis**

<img width="30" src="/img/resources/aws/ml/deep-learning-containers.png" alt="DeepLearningContainers" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.DeepLearningContainers**, **DLC** (alias)

<img width="30" src="/img/resources/aws/ml/deepcomposer.png" alt="Deepcomposer" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Deepcomposer**

<img width="30" src="/img/resources/aws/ml/deeplens.png" alt="Deeplens" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Deeplens**

<img width="30" src="/img/resources/aws/ml/deepracer.png" alt="Deepracer" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Deepracer**

<img width="30" src="/img/resources/aws/ml/elastic-inference.png" alt="ElasticInference" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.ElasticInference**

<img width="30" src="/img/resources/aws/ml/forecast.png" alt="Forecast" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Forecast**

<img width="30" src="/img/resources/aws/ml/fraud-detector.png" alt="FraudDetector" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.FraudDetector**

<img width="30" src="/img/resources/aws/ml/kendra.png" alt="Kendra" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Kendra**

<img width="30" src="/img/resources/aws/ml/lex.png" alt="Lex" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Lex**

<img width="30" src="/img/resources/aws/ml/machine-learning.png" alt="MachineLearning" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.MachineLearning**

<img width="30" src="/img/resources/aws/ml/personalize.png" alt="Personalize" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Personalize**

<img width="30" src="/img/resources/aws/ml/polly.png" alt="Polly" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Polly**

<img width="30" src="/img/resources/aws/ml/q.png" alt="Q" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Q**

<img width="30" src="/img/resources/aws/ml/rekognition-image.png" alt="RekognitionImage" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.RekognitionImage**

<img width="30" src="/img/resources/aws/ml/rekognition-video.png" alt="RekognitionVideo" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.RekognitionVideo**

<img width="30" src="/img/resources/aws/ml/rekognition.png" alt="Rekognition" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Rekognition**

<img width="30" src="/img/resources/aws/ml/sagemaker-ground-truth.png" alt="SagemakerGroundTruth" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.SagemakerGroundTruth**

<img width="30" src="/img/resources/aws/ml/sagemaker-model.png" alt="SagemakerModel" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.SagemakerModel**

<img width="30" src="/img/resources/aws/ml/sagemaker-notebook.png" alt="SagemakerNotebook" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.SagemakerNotebook**

<img width="30" src="/img/resources/aws/ml/sagemaker-training-job.png" alt="SagemakerTrainingJob" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.SagemakerTrainingJob**

<img width="30" src="/img/resources/aws/ml/sagemaker.png" alt="Sagemaker" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Sagemaker**

<img width="30" src="/img/resources/aws/ml/tensorflow-on-aws.png" alt="TensorflowOnAWS" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.TensorflowOnAWS**

<img width="30" src="/img/resources/aws/ml/textract.png" alt="Textract" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Textract**

<img width="30" src="/img/resources/aws/ml/transcribe.png" alt="Transcribe" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Transcribe**

<img width="30" src="/img/resources/aws/ml/translate.png" alt="Translate" style="float: left; padding-right: 5px;" >
**diagrams.aws.ml.Translate**

## aws.mobile


<img width="30" src="/img/resources/aws/mobile/amplify.png" alt="Amplify" style="float: left; padding-right: 5px;" >
**diagrams.aws.mobile.Amplify**

<img width="30" src="/img/resources/aws/mobile/api-gateway-endpoint.png" alt="APIGatewayEndpoint" style="float: left; padding-right: 5px;" >
**diagrams.aws.mobile.APIGatewayEndpoint**

<img width="30" src="/img/resources/aws/mobile/api-gateway.png" alt="APIGateway" style="float: left; padding-right: 5px;" >
**diagrams.aws.mobile.APIGateway**

<img width="30" src="/img/resources/aws/mobile/appsync.png" alt="Appsync" style="float: left; padding-right: 5px;" >
**diagrams.aws.mobile.Appsync**

<img width="30" src="/img/resources/aws/mobile/device-farm.png" alt="DeviceFarm" style="float: left; padding-right: 5px;" >
**diagrams.aws.mobile.DeviceFarm**

<img width="30" src="/img/resources/aws/mobile/mobile.png" alt="Mobile" style="float: left; padding-right: 5px;" >
**diagrams.aws.mobile.Mobile**

<img width="30" src="/img/resources/aws/mobile/pinpoint.png" alt="Pinpoint" style="float: left; padding-right: 5px;" >
**diagrams.aws.mobile.Pinpoint**

## aws.network


<img width="30" src="/img/resources/aws/network/api-gateway-endpoint.png" alt="APIGatewayEndpoint" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.APIGatewayEndpoint**

<img width="30" src="/img/resources/aws/network/api-gateway.png" alt="APIGateway" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.APIGateway**

<img width="30" src="/img/resources/aws/network/app-mesh.png" alt="AppMesh" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.AppMesh**

<img width="30" src="/img/resources/aws/network/client-vpn.png" alt="ClientVpn" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.ClientVpn**

<img width="30" src="/img/resources/aws/network/cloud-map.png" alt="CloudMap" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.CloudMap**

<img width="30" src="/img/resources/aws/network/cloudfront-download-distribution.png" alt="CloudFrontDownloadDistribution" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.CloudFrontDownloadDistribution**

<img width="30" src="/img/resources/aws/network/cloudfront-edge-location.png" alt="CloudFrontEdgeLocation" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.CloudFrontEdgeLocation**

<img width="30" src="/img/resources/aws/network/cloudfront-streaming-distribution.png" alt="CloudFrontStreamingDistribution" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.CloudFrontStreamingDistribution**

<img width="30" src="/img/resources/aws/network/cloudfront.png" alt="CloudFront" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.CloudFront**, **CF** (alias)

<img width="30" src="/img/resources/aws/network/direct-connect.png" alt="DirectConnect" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.DirectConnect**

<img width="30" src="/img/resources/aws/network/elastic-load-balancing.png" alt="ElasticLoadBalancing" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.ElasticLoadBalancing**, **ELB** (alias)

<img width="30" src="/img/resources/aws/network/elb-application-load-balancer.png" alt="ElbApplicationLoadBalancer" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.ElbApplicationLoadBalancer**, **ALB** (alias)

<img width="30" src="/img/resources/aws/network/elb-classic-load-balancer.png" alt="ElbClassicLoadBalancer" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.ElbClassicLoadBalancer**, **CLB** (alias)

<img width="30" src="/img/resources/aws/network/elb-network-load-balancer.png" alt="ElbNetworkLoadBalancer" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.ElbNetworkLoadBalancer**, **NLB** (alias)

<img width="30" src="/img/resources/aws/network/endpoint.png" alt="Endpoint" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.Endpoint**

<img width="30" src="/img/resources/aws/network/global-accelerator.png" alt="GlobalAccelerator" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.GlobalAccelerator**, **GAX** (alias)

<img width="30" src="/img/resources/aws/network/internet-gateway.png" alt="InternetGateway" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.InternetGateway**, **IGW** (alias)

<img width="30" src="/img/resources/aws/network/nacl.png" alt="Nacl" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.Nacl**

<img width="30" src="/img/resources/aws/network/nat-gateway.png" alt="NATGateway" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.NATGateway**

<img width="30" src="/img/resources/aws/network/network-firewall.png" alt="NetworkFirewall" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.NetworkFirewall**

<img width="30" src="/img/resources/aws/network/networking-and-content-delivery.png" alt="NetworkingAndContentDelivery" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.NetworkingAndContentDelivery**

<img width="30" src="/img/resources/aws/network/private-subnet.png" alt="PrivateSubnet" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.PrivateSubnet**

<img width="30" src="/img/resources/aws/network/privatelink.png" alt="Privatelink" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.Privatelink**

<img width="30" src="/img/resources/aws/network/public-subnet.png" alt="PublicSubnet" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.PublicSubnet**

<img width="30" src="/img/resources/aws/network/route-53-hosted-zone.png" alt="Route53HostedZone" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.Route53HostedZone**

<img width="30" src="/img/resources/aws/network/route-53.png" alt="Route53" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.Route53**

<img width="30" src="/img/resources/aws/network/route-table.png" alt="RouteTable" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.RouteTable**

<img width="30" src="/img/resources/aws/network/site-to-site-vpn.png" alt="SiteToSiteVpn" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.SiteToSiteVpn**

<img width="30" src="/img/resources/aws/network/transit-gateway-attachment.png" alt="TransitGatewayAttachment" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.TransitGatewayAttachment**, **TGWAttach** (alias)

<img width="30" src="/img/resources/aws/network/transit-gateway.png" alt="TransitGateway" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.TransitGateway**, **TGW** (alias)

<img width="30" src="/img/resources/aws/network/vpc-customer-gateway.png" alt="VPCCustomerGateway" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.VPCCustomerGateway**

<img width="30" src="/img/resources/aws/network/vpc-elastic-network-adapter.png" alt="VPCElasticNetworkAdapter" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.VPCElasticNetworkAdapter**

<img width="30" src="/img/resources/aws/network/vpc-elastic-network-interface.png" alt="VPCElasticNetworkInterface" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.VPCElasticNetworkInterface**

<img width="30" src="/img/resources/aws/network/vpc-flow-logs.png" alt="VPCFlowLogs" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.VPCFlowLogs**

<img width="30" src="/img/resources/aws/network/vpc-peering.png" alt="VPCPeering" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.VPCPeering**

<img width="30" src="/img/resources/aws/network/vpc-router.png" alt="VPCRouter" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.VPCRouter**

<img width="30" src="/img/resources/aws/network/vpc-traffic-mirroring.png" alt="VPCTrafficMirroring" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.VPCTrafficMirroring**

<img width="30" src="/img/resources/aws/network/vpc.png" alt="VPC" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.VPC**

<img width="30" src="/img/resources/aws/network/vpn-connection.png" alt="VpnConnection" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.VpnConnection**

<img width="30" src="/img/resources/aws/network/vpn-gateway.png" alt="VpnGateway" style="float: left; padding-right: 5px;" >
**diagrams.aws.network.VpnGateway**

## aws.quantum


<img width="30" src="/img/resources/aws/quantum/braket.png" alt="Braket" style="float: left; padding-right: 5px;" >
**diagrams.aws.quantum.Braket**

<img width="30" src="/img/resources/aws/quantum/quantum-technologies.png" alt="QuantumTechnologies" style="float: left; padding-right: 5px;" >
**diagrams.aws.quantum.QuantumTechnologies**

## aws.robotics


<img width="30" src="/img/resources/aws/robotics/robomaker-cloud-extension-ros.png" alt="RobomakerCloudExtensionRos" style="float: left; padding-right: 5px;" >
**diagrams.aws.robotics.RobomakerCloudExtensionRos**

<img width="30" src="/img/resources/aws/robotics/robomaker-development-environment.png" alt="RobomakerDevelopmentEnvironment" style="float: left; padding-right: 5px;" >
**diagrams.aws.robotics.RobomakerDevelopmentEnvironment**

<img width="30" src="/img/resources/aws/robotics/robomaker-fleet-management.png" alt="RobomakerFleetManagement" style="float: left; padding-right: 5px;" >
**diagrams.aws.robotics.RobomakerFleetManagement**

<img width="30" src="/img/resources/aws/robotics/robomaker-simulator.png" alt="RobomakerSimulator" style="float: left; padding-right: 5px;" >
**diagrams.aws.robotics.RobomakerSimulator**

<img width="30" src="/img/resources/aws/robotics/robomaker.png" alt="Robomaker" style="float: left; padding-right: 5px;" >
**diagrams.aws.robotics.Robomaker**

<img width="30" src="/img/resources/aws/robotics/robotics.png" alt="Robotics" style="float: left; padding-right: 5px;" >
**diagrams.aws.robotics.Robotics**

## aws.satellite


<img width="30" src="/img/resources/aws/satellite/ground-station.png" alt="GroundStation" style="float: left; padding-right: 5px;" >
**diagrams.aws.satellite.GroundStation**

<img width="30" src="/img/resources/aws/satellite/satellite.png" alt="Satellite" style="float: left; padding-right: 5px;" >
**diagrams.aws.satellite.Satellite**

## aws.security


<img width="30" src="/img/resources/aws/security/ad-connector.png" alt="AdConnector" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.AdConnector**

<img width="30" src="/img/resources/aws/security/artifact.png" alt="Artifact" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.Artifact**

<img width="30" src="/img/resources/aws/security/certificate-authority.png" alt="CertificateAuthority" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.CertificateAuthority**

<img width="30" src="/img/resources/aws/security/certificate-manager.png" alt="CertificateManager" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.CertificateManager**, **ACM** (alias)

<img width="30" src="/img/resources/aws/security/cloud-directory.png" alt="CloudDirectory" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.CloudDirectory**

<img width="30" src="/img/resources/aws/security/cloudhsm.png" alt="Cloudhsm" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.Cloudhsm**, **CloudHSM** (alias)

<img width="30" src="/img/resources/aws/security/cognito.png" alt="Cognito" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.Cognito**

<img width="30" src="/img/resources/aws/security/detective.png" alt="Detective" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.Detective**

<img width="30" src="/img/resources/aws/security/directory-service.png" alt="DirectoryService" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.DirectoryService**, **DS** (alias)

<img width="30" src="/img/resources/aws/security/firewall-manager.png" alt="FirewallManager" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.FirewallManager**, **FMS** (alias)

<img width="30" src="/img/resources/aws/security/guardduty.png" alt="Guardduty" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.Guardduty**

<img width="30" src="/img/resources/aws/security/identity-and-access-management-iam-access-analyzer.png" alt="IdentityAndAccessManagementIamAccessAnalyzer" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.IdentityAndAccessManagementIamAccessAnalyzer**, **IAMAccessAnalyzer** (alias)

<img width="30" src="/img/resources/aws/security/identity-and-access-management-iam-add-on.png" alt="IdentityAndAccessManagementIamAddOn" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.IdentityAndAccessManagementIamAddOn**

<img width="30" src="/img/resources/aws/security/identity-and-access-management-iam-aws-sts-alternate.png" alt="IdentityAndAccessManagementIamAWSStsAlternate" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.IdentityAndAccessManagementIamAWSStsAlternate**

<img width="30" src="/img/resources/aws/security/identity-and-access-management-iam-aws-sts.png" alt="IdentityAndAccessManagementIamAWSSts" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.IdentityAndAccessManagementIamAWSSts**, **IAMAWSSts** (alias)

<img width="30" src="/img/resources/aws/security/identity-and-access-management-iam-data-encryption-key.png" alt="IdentityAndAccessManagementIamDataEncryptionKey" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.IdentityAndAccessManagementIamDataEncryptionKey**

<img width="30" src="/img/resources/aws/security/identity-and-access-management-iam-encrypted-data.png" alt="IdentityAndAccessManagementIamEncryptedData" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.IdentityAndAccessManagementIamEncryptedData**

<img width="30" src="/img/resources/aws/security/identity-and-access-management-iam-long-term-security-credential.png" alt="IdentityAndAccessManagementIamLongTermSecurityCredential" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.IdentityAndAccessManagementIamLongTermSecurityCredential**

<img width="30" src="/img/resources/aws/security/identity-and-access-management-iam-mfa-token.png" alt="IdentityAndAccessManagementIamMfaToken" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.IdentityAndAccessManagementIamMfaToken**

<img width="30" src="/img/resources/aws/security/identity-and-access-management-iam-permissions.png" alt="IdentityAndAccessManagementIamPermissions" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.IdentityAndAccessManagementIamPermissions**, **IAMPermissions** (alias)

<img width="30" src="/img/resources/aws/security/identity-and-access-management-iam-role.png" alt="IdentityAndAccessManagementIamRole" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.IdentityAndAccessManagementIamRole**, **IAMRole** (alias)

<img width="30" src="/img/resources/aws/security/identity-and-access-management-iam-temporary-security-credential.png" alt="IdentityAndAccessManagementIamTemporarySecurityCredential" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.IdentityAndAccessManagementIamTemporarySecurityCredential**

<img width="30" src="/img/resources/aws/security/identity-and-access-management-iam.png" alt="IdentityAndAccessManagementIam" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.IdentityAndAccessManagementIam**, **IAM** (alias)

<img width="30" src="/img/resources/aws/security/inspector-agent.png" alt="InspectorAgent" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.InspectorAgent**

<img width="30" src="/img/resources/aws/security/inspector.png" alt="Inspector" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.Inspector**

<img width="30" src="/img/resources/aws/security/key-management-service.png" alt="KeyManagementService" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.KeyManagementService**, **KMS** (alias)

<img width="30" src="/img/resources/aws/security/macie.png" alt="Macie" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.Macie**

<img width="30" src="/img/resources/aws/security/managed-microsoft-ad.png" alt="ManagedMicrosoftAd" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.ManagedMicrosoftAd**

<img width="30" src="/img/resources/aws/security/resource-access-manager.png" alt="ResourceAccessManager" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.ResourceAccessManager**, **RAM** (alias)

<img width="30" src="/img/resources/aws/security/secrets-manager.png" alt="SecretsManager" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.SecretsManager**

<img width="30" src="/img/resources/aws/security/security-hub-finding.png" alt="SecurityHubFinding" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.SecurityHubFinding**

<img width="30" src="/img/resources/aws/security/security-hub.png" alt="SecurityHub" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.SecurityHub**

<img width="30" src="/img/resources/aws/security/security-identity-and-compliance.png" alt="SecurityIdentityAndCompliance" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.SecurityIdentityAndCompliance**

<img width="30" src="/img/resources/aws/security/shield-advanced.png" alt="ShieldAdvanced" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.ShieldAdvanced**

<img width="30" src="/img/resources/aws/security/shield.png" alt="Shield" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.Shield**

<img width="30" src="/img/resources/aws/security/simple-ad.png" alt="SimpleAd" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.SimpleAd**

<img width="30" src="/img/resources/aws/security/single-sign-on.png" alt="SingleSignOn" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.SingleSignOn**

<img width="30" src="/img/resources/aws/security/waf-filtering-rule.png" alt="WAFFilteringRule" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.WAFFilteringRule**

<img width="30" src="/img/resources/aws/security/waf.png" alt="WAF" style="float: left; padding-right: 5px;" >
**diagrams.aws.security.WAF**

## aws.storage


<img width="30" src="/img/resources/aws/storage/backup.png" alt="Backup" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.Backup**

<img width="30" src="/img/resources/aws/storage/cloudendure-disaster-recovery.png" alt="CloudendureDisasterRecovery" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.CloudendureDisasterRecovery**, **CDR** (alias)

<img width="30" src="/img/resources/aws/storage/efs-infrequentaccess-primary-bg.png" alt="EFSInfrequentaccessPrimaryBg" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.EFSInfrequentaccessPrimaryBg**

<img width="30" src="/img/resources/aws/storage/efs-standard-primary-bg.png" alt="EFSStandardPrimaryBg" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.EFSStandardPrimaryBg**

<img width="30" src="/img/resources/aws/storage/elastic-block-store-ebs-snapshot.png" alt="ElasticBlockStoreEBSSnapshot" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.ElasticBlockStoreEBSSnapshot**

<img width="30" src="/img/resources/aws/storage/elastic-block-store-ebs-volume.png" alt="ElasticBlockStoreEBSVolume" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.ElasticBlockStoreEBSVolume**

<img width="30" src="/img/resources/aws/storage/elastic-block-store-ebs.png" alt="ElasticBlockStoreEBS" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.ElasticBlockStoreEBS**, **EBS** (alias)

<img width="30" src="/img/resources/aws/storage/elastic-file-system-efs-file-system.png" alt="ElasticFileSystemEFSFileSystem" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.ElasticFileSystemEFSFileSystem**

<img width="30" src="/img/resources/aws/storage/elastic-file-system-efs.png" alt="ElasticFileSystemEFS" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.ElasticFileSystemEFS**, **EFS** (alias)

<img width="30" src="/img/resources/aws/storage/fsx-for-lustre.png" alt="FsxForLustre" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.FsxForLustre**

<img width="30" src="/img/resources/aws/storage/fsx-for-windows-file-server.png" alt="FsxForWindowsFileServer" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.FsxForWindowsFileServer**

<img width="30" src="/img/resources/aws/storage/fsx.png" alt="Fsx" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.Fsx**, **FSx** (alias)

<img width="30" src="/img/resources/aws/storage/multiple-volumes-resource.png" alt="MultipleVolumesResource" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.MultipleVolumesResource**

<img width="30" src="/img/resources/aws/storage/s3-access-points.png" alt="S3AccessPoints" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.S3AccessPoints**

<img width="30" src="/img/resources/aws/storage/s3-glacier-archive.png" alt="S3GlacierArchive" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.S3GlacierArchive**

<img width="30" src="/img/resources/aws/storage/s3-glacier-vault.png" alt="S3GlacierVault" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.S3GlacierVault**

<img width="30" src="/img/resources/aws/storage/s3-glacier.png" alt="S3Glacier" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.S3Glacier**

<img width="30" src="/img/resources/aws/storage/s3-object-lambda-access-points.png" alt="S3ObjectLambdaAccessPoints" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.S3ObjectLambdaAccessPoints**

<img width="30" src="/img/resources/aws/storage/simple-storage-service-s3-bucket-with-objects.png" alt="SimpleStorageServiceS3BucketWithObjects" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.SimpleStorageServiceS3BucketWithObjects**

<img width="30" src="/img/resources/aws/storage/simple-storage-service-s3-bucket.png" alt="SimpleStorageServiceS3Bucket" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.SimpleStorageServiceS3Bucket**

<img width="30" src="/img/resources/aws/storage/simple-storage-service-s3-object.png" alt="SimpleStorageServiceS3Object" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.SimpleStorageServiceS3Object**

<img width="30" src="/img/resources/aws/storage/simple-storage-service-s3.png" alt="SimpleStorageServiceS3" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.SimpleStorageServiceS3**, **S3** (alias)

<img width="30" src="/img/resources/aws/storage/snow-family-snowball-import-export.png" alt="SnowFamilySnowballImportExport" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.SnowFamilySnowballImportExport**

<img width="30" src="/img/resources/aws/storage/snowball-edge.png" alt="SnowballEdge" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.SnowballEdge**

<img width="30" src="/img/resources/aws/storage/snowball.png" alt="Snowball" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.Snowball**

<img width="30" src="/img/resources/aws/storage/snowmobile.png" alt="Snowmobile" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.Snowmobile**

<img width="30" src="/img/resources/aws/storage/storage-gateway-cached-volume.png" alt="StorageGatewayCachedVolume" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.StorageGatewayCachedVolume**

<img width="30" src="/img/resources/aws/storage/storage-gateway-non-cached-volume.png" alt="StorageGatewayNonCachedVolume" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.StorageGatewayNonCachedVolume**

<img width="30" src="/img/resources/aws/storage/storage-gateway-virtual-tape-library.png" alt="StorageGatewayVirtualTapeLibrary" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.StorageGatewayVirtualTapeLibrary**

<img width="30" src="/img/resources/aws/storage/storage-gateway.png" alt="StorageGateway" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.StorageGateway**

<img width="30" src="/img/resources/aws/storage/storage.png" alt="Storage" style="float: left; padding-right: 5px;" >
**diagrams.aws.storage.Storage**
