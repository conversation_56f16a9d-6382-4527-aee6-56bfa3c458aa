# ConnectX - <PERSON><PERSON><PERSON> hình <PERSON>inx High Availability

## Tổng quan kiến trúc

<PERSON> hình triển khai sử dụng **Nginx làm HA Proxy** với các **All-in-One Application Servers**. Mỗi server ch<PERSON><PERSON> đ<PERSON><PERSON> đ<PERSON> các thành phần: Nginx Web Server, Vue.js Frontend, PHP Backend, và RADIUS Server.

## 1. Nginx HA Proxy Layer

### 1.1 Keepalived Configuration

#### Master Node (*********):
```bash
# /etc/keepalived/keepalived.conf
vrrp_script chk_nginx {
    script "/usr/bin/killall -0 nginx"
    interval 2
    weight 2
    fall 3
    rise 2
}

vrrp_instance VI_1 {
    state MASTER
    interface eth0
    virtual_router_id 51
    priority 100
    advert_int 1
    authentication {
        auth_type PASS
        auth_pass connectx2024
    }
    virtual_ipaddress {
        **********
    }
    track_script {
        chk_nginx
    }
}
```

#### Backup Node (*********):
```bash
# /etc/keepalived/keepalived.conf
vrrp_script chk_nginx {
    script "/usr/bin/killall -0 nginx"
    interval 2
    weight 2
    fall 3
    rise 2
}

vrrp_instance VI_1 {
    state BACKUP
    interface eth0
    virtual_router_id 51
    priority 90
    advert_int 1
    authentication {
        auth_type PASS
        auth_pass connectx2024
    }
    virtual_ipaddress {
        **********
    }
    track_script {
        chk_nginx
    }
}
```

### 1.2 Nginx HA Proxy Configuration

```nginx
# /etc/nginx/nginx.conf - HA Proxy Nodes

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # Performance tuning
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript 
               application/javascript application/xml+rss 
               application/json application/xml;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    
    # Upstream backend servers
    upstream backend_servers {
        least_conn;
        server *********:80 weight=1 max_fails=3 fail_timeout=30s;
        server *********:80 weight=1 max_fails=3 fail_timeout=30s;
        server *********:80 weight=1 max_fails=3 fail_timeout=30s;
        
        # Health check
        keepalive 32;
    }
    
    # RADIUS upstream
    upstream radius_servers {
        server *********:1812 weight=1 max_fails=2 fail_timeout=10s;
        server *********:1812 weight=1 max_fails=2 fail_timeout=10s;
        server *********:1812 weight=1 max_fails=2 fail_timeout=10s;
    }
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Main server block
    server {
        listen 80;
        listen 443 ssl http2;
        server_name connectx.local *.connectx.local;
        
        # SSL certificate
        ssl_certificate /etc/letsencrypt/live/connectx.local/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/connectx.local/privkey.pem;
        
        # Redirect HTTP to HTTPS
        if ($scheme != "https") {
            return 301 https://$host$request_uri;
        }
        
        # Health check endpoint
        location /nginx-health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Admin CMS
        location /admin {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://backend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # Captive Portal
        location /portal {
            proxy_pass http://backend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # API endpoints
        location /api {
            limit_req zone=api burst=50 nodelay;
            proxy_pass http://backend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # CORS headers
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
            
            if ($request_method = 'OPTIONS') {
                return 204;
            }
        }
        
        # Login rate limiting
        location /api/auth/login {
            limit_req zone=login burst=5 nodelay;
            proxy_pass http://backend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Static files
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://backend_servers;
            proxy_cache_valid 200 1d;
            expires 1d;
            add_header Cache-Control "public, immutable";
        }
        
        # Default location
        location / {
            proxy_pass http://backend_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}

# Stream block for RADIUS load balancing
stream {
    upstream radius_auth {
        server *********:1812 weight=1 max_fails=2 fail_timeout=10s;
        server *********:1812 weight=1 max_fails=2 fail_timeout=10s;
        server *********:1812 weight=1 max_fails=2 fail_timeout=10s;
    }
    
    upstream radius_acct {
        server *********:1813 weight=1 max_fails=2 fail_timeout=10s;
        server *********:1813 weight=1 max_fails=2 fail_timeout=10s;
        server *********:1813 weight=1 max_fails=2 fail_timeout=10s;
    }
    
    server {
        listen 1812 udp;
        proxy_pass radius_auth;
        proxy_timeout 3s;
        proxy_responses 1;
    }
    
    server {
        listen 1813 udp;
        proxy_pass radius_acct;
        proxy_timeout 3s;
        proxy_responses 1;
    }
}
```

## 2. All-in-One Application Server Configuration

### 2.1 Nginx Web Server trên mỗi App Server

```nginx
# /etc/nginx/sites-available/connectx - Trên mỗi App Server

server {
    listen 80;
    server_name _;
    root /var/www/connectx/public;
    index index.php index.html;
    
    # Health check
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Admin CMS (Vue.js)
    location /admin {
        try_files $uri $uri/ /admin/index.html;
        
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Captive Portal (Vue.js)
    location /portal {
        try_files $uri $uri/ /portal/index.html;
        
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API endpoints (PHP-FPM)
    location /api {
        try_files $uri $uri/ /index.php?$query_string;
        
        location ~ \.php$ {
            fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi_params;
            
            # PHP-FPM health check
            fastcgi_param PHP_ADMIN_VALUE "auto_prepend_file=/var/www/connectx/health_check.php";
        }
    }
    
    # PHP files
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }
    
    location ~ /(config|logs|vendor) {
        deny all;
    }
}
```

### 2.2 PHP-FPM Configuration

```ini
# /etc/php/8.1/fpm/pool.d/connectx.conf

[connectx]
user = www-data
group = www-data

listen = /var/run/php/php8.1-fpm.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500

; Health check
ping.path = /ping
ping.response = pong

; Status page
pm.status_path = /status

; Slow log
slowlog = /var/log/php8.1-fpm-slow.log
request_slowlog_timeout = 10s

; Environment variables
env[HOSTNAME] = $HOSTNAME
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp

; PHP admin values
php_admin_value[error_log] = /var/log/php8.1-fpm.log
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = 256M
php_admin_value[max_execution_time] = 300
```

## 3. Database Configuration

### 3.1 MySQL Master-Slave Setup

#### MySQL Master (*********):
```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf

[mysqld]
server-id = 1
log-bin = mysql-bin
binlog-format = ROW
gtid-mode = ON
enforce-gtid-consistency = ON

# Replication
replicate-do-db = connectx
slave-skip-errors = 1062

# Performance
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 1
sync_binlog = 1

# Connections
max_connections = 500
max_connect_errors = 1000000
```

#### MySQL Slaves (*********, *********):
```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf

[mysqld]
server-id = 2  # 3 for second slave
log-bin = mysql-bin
binlog-format = ROW
gtid-mode = ON
enforce-gtid-consistency = ON

# Read-only slave
read-only = 1
super-read-only = 1

# Replication
replicate-do-db = connectx
slave-skip-errors = 1062

# Performance
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
```

### 3.2 Redis Configuration

#### Redis Master (*********):
```redis
# /etc/redis/redis.conf

port 6379
bind 0.0.0.0
protected-mode yes
requirepass connectx_redis_2024

# Persistence
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec

# Memory
maxmemory 1gb
maxmemory-policy allkeys-lru

# Replication
replica-read-only yes
```

#### Redis Sentinel (*********):
```redis
# /etc/redis/sentinel.conf

port 26379
sentinel monitor mymaster ********* 6379 2
sentinel auth-pass mymaster connectx_redis_2024
sentinel down-after-milliseconds mymaster 5000
sentinel failover-timeout mymaster 10000
sentinel parallel-syncs mymaster 1
```

## 4. Monitoring & Health Checks

### 4.1 Health Check Script

```bash
#!/bin/bash
# /usr/local/bin/connectx_health_check.sh

# Check Nginx
if ! pgrep nginx > /dev/null; then
    echo "CRITICAL: Nginx is not running"
    exit 2
fi

# Check PHP-FPM
if ! pgrep php-fpm > /dev/null; then
    echo "CRITICAL: PHP-FPM is not running"
    exit 2
fi

# Check RADIUS
if ! pgrep freeradius > /dev/null; then
    echo "WARNING: FreeRADIUS is not running"
    exit 1
fi

# Check database connection
mysql -h *********0 -u connectx -p'password' -e "SELECT 1" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "CRITICAL: Cannot connect to database"
    exit 2
fi

# Check Redis connection
redis-cli -h ********* -a connectx_redis_2024 ping > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "WARNING: Cannot connect to Redis"
    exit 1
fi

echo "OK: All services are healthy"
exit 0
```

## 5. Deployment Commands

### 5.1 Khởi động services theo thứ tự:

```bash
# 1. Start database layer
systemctl start mysql
systemctl start redis-server
systemctl start redis-sentinel

# 2. Start application services
systemctl start php8.1-fpm
systemctl start nginx
systemctl start freeradius

# 3. Start HA proxy layer
systemctl start keepalived
systemctl start nginx  # On HA proxy nodes
```

### 5.2 Kiểm tra trạng thái:

```bash
# Check VIP status
ip addr show | grep **********

# Check upstream health
curl -H "Host: connectx.local" http://**********/nginx-health

# Check application health
curl -H "Host: connectx.local" http://**********/health
```

---

**Mô hình này cung cấp:**
- ✅ High Availability với Nginx HA Proxy
- ✅ All-in-One servers dễ quản lý
- ✅ Auto failover < 5 giây
- ✅ Load balancing và health check
- ✅ SSL/TLS termination
- ✅ Rate limiting và security
- ✅ Database và Cache HA
- ✅ Monitoring và logging
