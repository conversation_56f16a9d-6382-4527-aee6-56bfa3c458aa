# ConnectX - Hướng dẫn cấu hình High Availability

## Tổng quan kiến trúc HA

Hệ thống ConnectX được thiết kế với kiến trúc High Availability đầy đủ, đả<PERSON> bảo uptime 99.9% và khả năng chịu lỗi cao. Tất cả các thành phần quan trọng đều được triển khai theo mô hình redundant.

## 1. Tầng Firewall - pfSense VRRP

### Cấu hình Active-Passive
```
Primary pfSense:
- IP: ********
- VRRP Priority: 100
- Virtual IP: ********

Secondary pfSense:
- IP: ********  
- VRRP Priority: 90
- Virtual IP: ********
```

### Thông số kỹ thuật:
- **Protocol**: VRRP (Virtual Router Redundancy Protocol)
- **Heartbeat**: **********:112
- **Check interval**: 1 giây
- **Failover time**: < 3 giây
- **Preemption**: Enabled

## 2. T<PERSON><PERSON> Load Balancer - HAProxy + Keepalived

### C<PERSON><PERSON> hình Active-Active
```
HAProxy Master:
- IP: ********0
- Priority: 100
- Virtual IP: ********00

HAProxy Backup:
- IP: ********1
- Priority: 90
- Virtual IP: ********00
```

### Cấu hình HAProxy:
```haproxy
global
    daemon
    maxconn 4096
    log stdout local0

defaults
    mode http
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms
    option httplog

frontend web_frontend
    bind *:80
    bind *:443 ssl crt /etc/ssl/certs/connectx.pem
    redirect scheme https if !{ ssl_fc }
    default_backend web_servers

backend web_servers
    balance roundrobin
    option httpchk GET /health
    server web1 *********:80 check
    server web2 *********:80 check
    server web3 *********:80 check
```

## 3. Tầng Web Server - Nginx Cluster

### Cấu hình 3 nodes:
```
Web Server 1: *********
Web Server 2: *********
Web Server 3: *********
```

### Nginx Configuration:
```nginx
upstream php_backend {
    server *********:9000;
    server *********:9000;
    server *********:9000;
}

server {
    listen 80;
    server_name connectx.local;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass php_backend;
        fastcgi_index index.php;
        include fastcgi_params;
    }
    
    location /health {
        access_log off;
        return 200 "healthy\n";
    }
}
```

## 4. Tầng Application - PHP-FPM Cluster

### Cấu hình 3 nodes:
```
App Server 1: *********
App Server 2: *********  
App Server 3: *********
```

### PHP-FPM Pool Configuration:
```ini
[connectx]
user = www-data
group = www-data
listen = 9000
listen.allowed_clients = ********/24

pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500

ping.path = /ping
ping.response = pong
```

## 5. RADIUS Cluster - FreeRADIUS

### Cấu hình Active-Active-Active:
```
RADIUS 1: *********
RADIUS 2: *********
RADIUS 3: *********
```

### Clients Configuration:
```
client wifi_controllers {
    ipaddr = ***********/16
    secret = radius_shared_secret
    require_message_authenticator = yes
}
```

### SQL Module Configuration:
```
sql {
    driver = "mysql"
    server = "**********"  # MySQL VIP
    port = 3306
    login = "radius"
    password = "radius_password"
    radius_db = "radius"
    
    read_clients = yes
    client_table = "nas"
}
```

## 6. Redis Cluster - Master-Slave + Sentinel

### Cấu hình Redis:
```
Redis Master 1: *********:6379
Redis Slave 1:  *********:6379
Redis Master 2: *********:6380
Redis Slave 2:  *********:6380
Redis Sentinel: *********:26379
```

### Redis Master Configuration:
```redis
port 6379
bind 0.0.0.0
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec
```

### Redis Sentinel Configuration:
```redis
port 26379
sentinel monitor mymaster ********* 6379 2
sentinel down-after-milliseconds mymaster 5000
sentinel failover-timeout mymaster 10000
sentinel parallel-syncs mymaster 1
```

## 7. MySQL Cluster - Master-Master + Read Replicas

### Cấu hình Database:
```
MySQL Master 1: *********
MySQL Master 2: *********
MySQL Slave 1:  *********
MySQL Slave 2:  *********
ProxySQL:       ********** (VIP)
```

### MySQL Master-Master Configuration:

#### Master 1 (my.cnf):
```ini
[mysqld]
server-id = 1
log-bin = mysql-bin
binlog-format = ROW
gtid-mode = ON
enforce-gtid-consistency = ON
auto_increment_offset = 1
auto_increment_increment = 2

# Replication
replicate-do-db = connectx
slave-skip-errors = 1062
```

#### Master 2 (my.cnf):
```ini
[mysqld]
server-id = 2
log-bin = mysql-bin
binlog-format = ROW
gtid-mode = ON
enforce-gtid-consistency = ON
auto_increment_offset = 2
auto_increment_increment = 2

# Replication
replicate-do-db = connectx
slave-skip-errors = 1062
```

### ProxySQL Configuration:
```sql
INSERT INTO mysql_servers(hostgroup_id, hostname, port, weight) VALUES
(0, '*********', 3306, 1000),
(0, '*********', 3306, 1000),
(1, '*********', 3306, 900),
(1, '*********', 3306, 900);

INSERT INTO mysql_query_rules(rule_id, active, match_pattern, destination_hostgroup, apply) VALUES
(1, 1, '^SELECT.*', 1, 1),
(2, 1, '^INSERT|UPDATE|DELETE.*', 0, 1);
```

## 8. Monitoring Stack

### Prometheus Configuration:
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'connectx-web'
    static_configs:
      - targets: ['*********:9090', '*********:9090', '*********:9090']
  
  - job_name: 'connectx-app'
    static_configs:
      - targets: ['*********:9091', '*********:9091', '*********:9091']
  
  - job_name: 'connectx-db'
    static_configs:
      - targets: ['*********:9104', '*********:9104']
```

## 9. Network VLAN Configuration

### VLAN Segmentation:
```
VLAN 1:   Management (10.0.0.0/24)
VLAN 10:  DMZ (********/24)
VLAN 20:  Web Tier (********/24)
VLAN 30:  App Tier (********/24)
VLAN 40:  Database Tier (********/24)
VLAN 100: Monitoring (**********/24)
```

## 10. Backup & Recovery

### Backup Schedule:
- **MySQL**: Full backup daily 2:00 AM, incremental hourly
- **Redis**: RDB snapshot every 15 minutes
- **Application files**: Daily rsync to backup server
- **Configuration**: Git repository với auto-commit

### Recovery Procedures:
1. **Database Recovery**: Point-in-time recovery từ backup + binlog
2. **Application Recovery**: Blue-green deployment từ backup
3. **Configuration Recovery**: Git restore + Ansible playbook

## 11. Security Hardening

### SSL/TLS Configuration:
```nginx
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
```

### Firewall Rules:
```bash
# Allow HTTP/HTTPS from internet
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# Allow MySQL replication between masters
iptables -A INPUT -s ********/24 -p tcp --dport 3306 -j ACCEPT

# Allow Redis cluster communication
iptables -A INPUT -s ********/24 -p tcp --dport 6379 -j ACCEPT
iptables -A INPUT -s ********/24 -p tcp --dport 26379 -j ACCEPT
```

## 12. Performance Tuning

### System Limits:
```bash
# /etc/security/limits.conf
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
```

### Kernel Parameters:
```bash
# /etc/sysctl.conf
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.core.netdev_max_backlog = 5000
vm.swappiness = 10
```

---

**Lưu ý**: Tài liệu này cung cấp cấu hình cơ bản. Trong môi trường production, cần điều chỉnh các thông số dựa trên tải thực tế và yêu cầu cụ thể của hệ thống.
