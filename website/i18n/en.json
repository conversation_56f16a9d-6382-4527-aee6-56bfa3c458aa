{"_comment": "This file is auto-generated by write-translations.js", "localized-strings": {"next": "Next", "previous": "Previous", "tagline": "Diagram as Code", "docs": {"getting-started/examples": {"title": "Examples"}, "getting-started/installation": {"title": "Installation"}, "guides/cluster": {"title": "Clusters"}, "guides/diagram": {"title": "Diagrams"}, "guides/edge": {"title": "<PERSON>s"}, "guides/node": {"title": "Nodes"}, "nodes/alibabacloud": {"title": "AlibabaCloud"}, "nodes/aws": {"title": "AWS"}, "nodes/azure": {"title": "Azure"}, "nodes/c4": {"title": "C4"}, "nodes/custom": {"title": "Custom"}, "nodes/digitalocean": {"title": "DigitalOcean"}, "nodes/elastic": {"title": "Elastic"}, "nodes/firebase": {"title": "Firebase"}, "nodes/gcp": {"title": "GCP"}, "nodes/generic": {"title": "Generic"}, "nodes/gis": {"title": "GIS"}, "nodes/ibm": {"title": "IBM"}, "nodes/k8s": {"title": "K8S"}, "nodes/oci": {"title": "OCI"}, "nodes/onprem": {"title": "OnPrem"}, "nodes/openstack": {"title": "OpenStack"}, "nodes/outscale": {"title": "Outscale"}, "nodes/programming": {"title": "Programming"}, "nodes/saas": {"title": "Saas"}}, "links": {"Docs": "Docs", "Guides": "Guides", "Nodes": "Nodes", "GitHub": "GitHub", "Sponsoring": "Sponsoring"}, "categories": {"Getting Started": "Getting Started", "Guides": "Guides", "Nodes": "Nodes"}}, "pages-strings": {"Help Translate|recruit community translators for your project": "Help Translate", "Edit this Doc|recruitment message asking to edit the doc source": "Edit", "Translate this Doc|recruitment message asking to translate the docs": "Translate"}}