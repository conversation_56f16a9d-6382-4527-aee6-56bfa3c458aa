#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> hình triển khai vật lý cho hệ thống Wifi Marketing ConnectX
Đảm bảo High Availability (HA) cho tất cả các thành phần
"""

from diagrams import Diagram, Cluster, Edge
from diagrams.onprem.client import Users, Client
from diagrams.onprem.network import Internet, Nginx, Haproxy
from diagrams.onprem.compute import Server
from diagrams.onprem.database import MySQL
from diagrams.onprem.inmemory import Redis
from diagrams.programming.framework import Vue
from diagrams.programming.language import PHP
from diagrams.onprem.monitoring import Grafana, Prometheus
from diagrams.generic.device import Mobile, Tablet
from diagrams.generic.network import Router, Switch, Firewall
from diagrams.custom import Custom
from diagrams.onprem.storage import Ceph
from diagrams.onprem.security import Vault
from diagrams.onprem.network import Pfsense

def create_physical_deployment():
    """Tạo mô hình triển khai vật lý với HA"""
    
    with Diagram("ConnectX - <PERSON><PERSON> hình triển khai vật lý với HA",
                 filename="connectx_physical_deployment",
                 show=False,
                 direction="TB",
                 graph_attr={
                     "fontsize": "14",
                     "fontname": "Arial",
                     "bgcolor": "white",
                     "pad": "0.8",
                     "nodesep": "0.8",
                     "ranksep": "1.5",
                     "splines": "ortho"
                 }):

        # Internet và CDN
        with Cluster("Internet & CDN"):
            internet = Internet("Internet")
            cdn = Custom("CloudFlare CDN", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/cloudflare.png")

        # DMZ Zone - Tầng bảo mật đầu tiên
        with Cluster("DMZ Zone"):
            # Firewall Cluster
            with Cluster("Firewall Cluster (Active-Passive)"):
                fw_primary = Pfsense("pfSense Primary")
                fw_secondary = Pfsense("pfSense Secondary")
            
            # Load Balancer Cluster
            with Cluster("Load Balancer Cluster (Active-Active)"):
                lb1 = Haproxy("HAProxy LB-1\n(*********)")
                lb2 = Haproxy("HAProxy LB-2\n(*********)")
                vip = Custom("Virtual IP\n(*********0)", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/keepalived.png")

        # Production Zone - Tầng ứng dụng
        with Cluster("Production Zone"):
            
            # Web Server Cluster
            with Cluster("Web Server Cluster"):
                web1 = Nginx("Nginx Web-1\n(*********)")
                web2 = Nginx("Nginx Web-2\n(*********)")
                web3 = Nginx("Nginx Web-3\n(*********)")
            
            # Application Server Cluster
            with Cluster("Application Server Cluster"):
                app1 = PHP("PHP-FPM App-1\n(*********)")
                app2 = PHP("PHP-FPM App-2\n(*********)")
                app3 = PHP("PHP-FPM App-3\n(*********)")
            
            # RADIUS Server Cluster
            with Cluster("RADIUS Cluster (Active-Active)"):
                radius1 = Server("FreeRADIUS-1\n(*********)")
                radius2 = Server("FreeRADIUS-2\n(*********)")
                radius3 = Server("FreeRADIUS-3\n(10.0.2.32)")

        # Cache Layer - Redis Cluster
        with Cluster("Cache Layer"):
            with Cluster("Redis Cluster (Master-Slave)"):
                redis_master1 = Redis("Redis Master-1\n(10.0.3.10)")
                redis_slave1 = Redis("Redis Slave-1\n(10.0.3.11)")
                redis_master2 = Redis("Redis Master-2\n(10.0.3.12)")
                redis_slave2 = Redis("Redis Slave-2\n(10.0.3.13)")
                redis_sentinel = Custom("Redis Sentinel\n(10.0.3.20)", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/redis.png")

        # Database Layer - MySQL Cluster
        with Cluster("Database Layer"):
            with Cluster("MySQL Cluster (Master-Master)"):
                mysql_master1 = MySQL("MySQL Master-1\n(10.0.4.10)")
                mysql_master2 = MySQL("MySQL Master-2\n(10.0.4.11)")
                mysql_slave1 = MySQL("MySQL Slave-1\n(10.0.4.12)")
                mysql_slave2 = MySQL("MySQL Slave-2\n(10.0.4.13)")
            
            # Shared Storage cho Database
            with Cluster("Shared Storage"):
                storage_primary = Ceph("SAN Storage Primary")
                storage_backup = Ceph("SAN Storage Backup")

        # Monitoring & Management Zone
        with Cluster("Monitoring & Management"):
            # Monitoring Stack
            with Cluster("Monitoring Stack"):
                prometheus = Prometheus("Prometheus\n(10.0.5.10)")
                grafana = Grafana("Grafana\n(10.0.5.11)")
                alertmanager = Custom("AlertManager\n(10.0.5.12)", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/prometheus.png")
            
            # Log Management
            with Cluster("Log Management"):
                elasticsearch = Custom("Elasticsearch\n(10.0.5.20)", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/elasticsearch.png")
                logstash = Custom("Logstash\n(10.0.5.21)", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/logstash.png")
                kibana = Custom("Kibana\n(10.0.5.22)", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/kibana.png")
            
            # Backup & Security
            backup_server = Server("Backup Server\n(10.0.5.30)")
            vault_server = Vault("HashiCorp Vault\n(10.0.5.31)")

        # External Services
        with Cluster("External Services"):
            zalo_service = Custom("Zalo Platform", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/zalo.png")
            sms_gateway = Custom("SMS Gateway", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/sms.png")
            payment_gateway = Custom("Payment Gateway", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/payment.png")

        # Network Infrastructure
        with Cluster("Network Infrastructure"):
            core_switch = Switch("Core Switch\n(Cisco 9300)")
            access_switch1 = Switch("Access Switch-1\n(Cisco 2960)")
            access_switch2 = Switch("Access Switch-2\n(Cisco 2960)")

        # Kết nối mạng cơ bản
        internet >> cdn >> fw_primary
        fw_primary >> Edge(label="VRRP", color="red", style="dashed") >> fw_secondary
        
        # Load Balancer HA
        fw_primary >> vip
        vip >> Edge(label="Active", color="green") >> lb1
        vip >> Edge(label="Active", color="green") >> lb2
        lb1 >> Edge(label="Keepalived", color="red", style="dashed") >> lb2

        # Web Server Load Balancing
        lb1 >> Edge(label="HTTP/HTTPS", color="blue") >> web1
        lb1 >> Edge(label="HTTP/HTTPS", color="blue") >> web2
        lb1 >> Edge(label="HTTP/HTTPS", color="blue") >> web3
        lb2 >> Edge(label="HTTP/HTTPS", color="blue") >> web1
        lb2 >> Edge(label="HTTP/HTTPS", color="blue") >> web2
        lb2 >> Edge(label="HTTP/HTTPS", color="blue") >> web3

        # Web to App Server
        web1 >> Edge(label="FastCGI", color="orange") >> app1
        web2 >> Edge(label="FastCGI", color="orange") >> app2
        web3 >> Edge(label="FastCGI", color="orange") >> app3

        # RADIUS Cluster connections
        app1 >> Edge(label="Auth", color="red") >> radius1
        app2 >> Edge(label="Auth", color="red") >> radius2
        app3 >> Edge(label="Auth", color="red") >> radius3
        
        # Cross-connections for HA
        radius1 >> Edge(label="Backup", style="dashed") >> radius2
        radius2 >> Edge(label="Backup", style="dashed") >> radius3

        # Redis Cluster connections
        app1 >> Edge(label="Cache", color="purple") >> redis_master1
        app2 >> Edge(label="Cache", color="purple") >> redis_master2
        app3 >> Edge(label="Cache", color="purple") >> redis_master1
        
        redis_master1 >> Edge(label="Replication", color="orange") >> redis_slave1
        redis_master2 >> Edge(label="Replication", color="orange") >> redis_slave2
        redis_sentinel >> Edge(label="Monitor", style="dotted") >> redis_master1
        redis_sentinel >> Edge(label="Monitor", style="dotted") >> redis_master2

        # MySQL Cluster connections
        app1 >> Edge(label="R/W", color="green") >> mysql_master1
        app2 >> Edge(label="R/W", color="green") >> mysql_master2
        app3 >> Edge(label="Read", color="blue") >> mysql_slave1
        
        mysql_master1 >> Edge(label="Master-Master", color="red") >> mysql_master2
        mysql_master1 >> Edge(label="Replication", color="orange") >> mysql_slave1
        mysql_master2 >> Edge(label="Replication", color="orange") >> mysql_slave2

        # Storage connections
        mysql_master1 >> Edge(label="Data", color="gray") >> storage_primary
        mysql_master2 >> Edge(label="Data", color="gray") >> storage_primary
        storage_primary >> Edge(label="Backup", style="dashed") >> storage_backup

        # Monitoring connections
        prometheus >> Edge(label="Metrics", style="dotted") >> app1
        prometheus >> Edge(label="Metrics", style="dotted") >> app2
        prometheus >> Edge(label="Metrics", style="dotted") >> app3
        prometheus >> Edge(label="Metrics", style="dotted") >> mysql_master1
        prometheus >> Edge(label="Metrics", style="dotted") >> redis_master1
        
        grafana >> Edge(label="Query", color="cyan") >> prometheus
        alertmanager >> Edge(label="Alerts", color="red") >> prometheus

        # Log collection
        logstash >> Edge(label="Logs", style="dotted") >> app1
        logstash >> Edge(label="Logs", style="dotted") >> app2
        logstash >> Edge(label="Logs", style="dotted") >> app3
        logstash >> Edge(label="Index", color="yellow") >> elasticsearch
        kibana >> Edge(label="Query", color="cyan") >> elasticsearch

        # External service connections
        app1 >> Edge(label="API", color="green") >> zalo_service
        app2 >> Edge(label="API", color="green") >> sms_gateway
        app3 >> Edge(label="API", color="green") >> payment_gateway

        # Backup connections
        backup_server >> Edge(label="Backup", style="dashed") >> mysql_master1
        backup_server >> Edge(label="Backup", style="dashed") >> storage_primary
        
        # Security
        vault_server >> Edge(label="Secrets", color="red") >> app1
        vault_server >> Edge(label="Secrets", color="red") >> app2
        vault_server >> Edge(label="Secrets", color="red") >> app3

def create_ha_details():
    """Tạo sơ đồ chi tiết về cấu hình HA"""

    with Diagram("ConnectX - Chi tiết cấu hình High Availability",
                 filename="connectx_ha_details",
                 show=False,
                 direction="LR",
                 graph_attr={
                     "fontsize": "12",
                     "fontname": "Arial",
                     "bgcolor": "white",
                     "pad": "0.5"
                 }):

        # Firewall HA Configuration
        with Cluster("Firewall HA (VRRP)"):
            fw_master = Pfsense("pfSense Master\nPriority: 100\nVIP: ********")
            fw_backup = Pfsense("pfSense Backup\nPriority: 90\nVIP: ********")
            fw_master >> Edge(label="VRRP Heartbeat\n**********:112", color="red") >> fw_backup

        # Load Balancer HA
        with Cluster("Load Balancer HA (Keepalived)"):
            lb_master = Haproxy("HAProxy Master\nPriority: 100\nVIP: *********0")
            lb_backup = Haproxy("HAProxy Backup\nPriority: 90\nVIP: *********0")
            lb_master >> Edge(label="VRRP Heartbeat\nCheck Interval: 2s", color="blue") >> lb_backup

        # Database HA Configuration
        with Cluster("MySQL HA (Master-Master + Slaves)"):
            db_m1 = MySQL("MySQL Master-1\nserver-id: 1\nauto_increment_offset: 1")
            db_m2 = MySQL("MySQL Master-2\nserver-id: 2\nauto_increment_offset: 2")
            db_s1 = MySQL("MySQL Slave-1\nserver-id: 3\nread-only: ON")
            db_s2 = MySQL("MySQL Slave-2\nserver-id: 4\nread-only: ON")

            db_m1 >> Edge(label="Binlog Replication\nGTID: ON", color="green") >> db_m2
            db_m2 >> Edge(label="Binlog Replication\nGTID: ON", color="green") >> db_m1
            db_m1 >> Edge(label="Async Replication", color="orange") >> db_s1
            db_m2 >> Edge(label="Async Replication", color="orange") >> db_s2

        # Redis HA Configuration
        with Cluster("Redis HA (Sentinel)"):
            redis_m1 = Redis("Redis Master-1\nPort: 6379")
            redis_s1 = Redis("Redis Slave-1\nPort: 6379")
            redis_m2 = Redis("Redis Master-2\nPort: 6380")
            redis_s2 = Redis("Redis Slave-2\nPort: 6380")
            sentinel = Custom("Redis Sentinel\nQuorum: 2\nPort: 26379", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/redis.png")

            redis_m1 >> Edge(label="Replication", color="red") >> redis_s1
            redis_m2 >> Edge(label="Replication", color="red") >> redis_s2
            sentinel >> Edge(label="Monitor & Failover", style="dotted") >> redis_m1
            sentinel >> Edge(label="Monitor & Failover", style="dotted") >> redis_m2

def create_network_topology():
    """Tạo sơ đồ topology mạng chi tiết"""

    with Diagram("ConnectX - Network Topology & VLAN",
                 filename="connectx_network_topology",
                 show=False,
                 direction="TB",
                 graph_attr={
                     "fontsize": "12",
                     "fontname": "Arial",
                     "bgcolor": "white",
                     "pad": "0.5"
                 }):

        # Core Network
        with Cluster("Core Network (VLAN 1)"):
            core_switch = Switch("Cisco 9300 Core\n10.0.0.0/24")

        # DMZ Network
        with Cluster("DMZ Network (VLAN 10)"):
            dmz_switch = Switch("DMZ Switch\n10.0.1.0/24")
            fw1 = Pfsense("Firewall-1\n10.0.1.1")
            fw2 = Pfsense("Firewall-2\n10.0.1.2")
            lb1 = Haproxy("LB-1\n*********")
            lb2 = Haproxy("LB-2\n*********")

        # Web Tier Network
        with Cluster("Web Tier (VLAN 20)"):
            web_switch = Switch("Web Switch\n10.0.2.0/24")
            web1 = Nginx("Web-1\n*********")
            web2 = Nginx("Web-2\n*********")
            web3 = Nginx("Web-3\n*********")

        # App Tier Network
        with Cluster("App Tier (VLAN 30)"):
            app_switch = Switch("App Switch\n10.0.3.0/24")
            app1 = PHP("App-1\n10.0.3.10")
            app2 = PHP("App-2\n10.0.3.11")
            app3 = PHP("App-3\n10.0.3.12")

        # Database Network
        with Cluster("Database Network (VLAN 40)"):
            db_switch = Switch("DB Switch\n10.0.4.0/24")
            db1 = MySQL("DB-1\n10.0.4.10")
            db2 = MySQL("DB-2\n10.0.4.11")
            redis1 = Redis("Redis-1\n10.0.4.20")
            redis2 = Redis("Redis-2\n10.0.4.21")

        # Management Network
        with Cluster("Management Network (VLAN 100)"):
            mgmt_switch = Switch("Mgmt Switch\n10.0.100.0/24")
            monitoring = Grafana("Monitoring\n10.0.100.10")
            backup = Server("Backup\n10.0.100.20")

        # Network connections
        core_switch >> dmz_switch
        core_switch >> web_switch
        core_switch >> app_switch
        core_switch >> db_switch
        core_switch >> mgmt_switch

        dmz_switch >> fw1
        dmz_switch >> fw2
        dmz_switch >> lb1
        dmz_switch >> lb2

        web_switch >> web1
        web_switch >> web2
        web_switch >> web3

        app_switch >> app1
        app_switch >> app2
        app_switch >> app3

        db_switch >> db1
        db_switch >> db2
        db_switch >> redis1
        db_switch >> redis2

        mgmt_switch >> monitoring
        mgmt_switch >> backup

if __name__ == "__main__":
    print("Đang tạo các mô hình triển khai vật lý cho hệ thống ConnectX...")

    # Tạo mô hình triển khai chính
    create_physical_deployment()
    print("✓ Đã tạo mô hình triển khai vật lý: connectx_physical_deployment.png")

    # Tạo chi tiết cấu hình HA
    create_ha_details()
    print("✓ Đã tạo chi tiết cấu hình HA: connectx_ha_details.png")

    # Tạo topology mạng
    create_network_topology()
    print("✓ Đã tạo topology mạng: connectx_network_topology.png")

    print("\n" + "="*60)
    print("THÔNG SỐ KỸ THUẬT HIGH AVAILABILITY")
    print("="*60)

    print("\n1. FIREWALL HA (pfSense VRRP):")
    print("   - Cấu hình: Active-Passive")
    print("   - Protocol: VRRP (Virtual Router Redundancy Protocol)")
    print("   - Virtual IP: ********")
    print("   - Heartbeat: **********:112")
    print("   - Failover time: < 3 giây")

    print("\n2. LOAD BALANCER HA (HAProxy + Keepalived):")
    print("   - Cấu hình: Active-Active")
    print("   - Virtual IP: *********0")
    print("   - Health check: HTTP/TCP every 2s")
    print("   - Failover time: < 5 giây")
    print("   - Session persistence: Sticky sessions")

    print("\n3. WEB SERVER CLUSTER (Nginx):")
    print("   - Số lượng: 3 nodes")
    print("   - Load balancing: Round-robin")
    print("   - Health check: HTTP 200 OK")
    print("   - Auto-scaling: Dựa trên CPU/Memory")

    print("\n4. APPLICATION SERVER CLUSTER (PHP-FPM):")
    print("   - Số lượng: 3 nodes")
    print("   - Process manager: Dynamic")
    print("   - Max children: 50 per node")
    print("   - Health check: FastCGI ping")

    print("\n5. RADIUS CLUSTER (FreeRADIUS):")
    print("   - Cấu hình: Active-Active-Active")
    print("   - Load balancing: DNS round-robin")
    print("   - Shared database: MySQL cluster")
    print("   - Failover: Client retry logic")

    print("\n6. REDIS CLUSTER (Master-Slave + Sentinel):")
    print("   - Master nodes: 2")
    print("   - Slave nodes: 2")
    print("   - Sentinel nodes: 3 (quorum: 2)")
    print("   - Failover time: < 10 giây")
    print("   - Data persistence: RDB + AOF")

    print("\n7. MYSQL CLUSTER (Master-Master + Slaves):")
    print("   - Master nodes: 2 (Active-Active)")
    print("   - Read replicas: 2")
    print("   - Replication: GTID-based")
    print("   - Failover: ProxySQL + Orchestrator")
    print("   - Backup: Daily full + hourly incremental")

    print("\n8. STORAGE HA:")
    print("   - Primary: SAN storage với RAID 10")
    print("   - Backup: Replicated SAN")
    print("   - Snapshot: Hourly")
    print("   - Retention: 30 days")

    print("\n9. MONITORING & ALERTING:")
    print("   - Metrics: Prometheus với 15s interval")
    print("   - Visualization: Grafana dashboards")
    print("   - Logs: ELK Stack (Elasticsearch cluster)")
    print("   - Alerting: AlertManager + PagerDuty")

    print("\n10. NETWORK HA:")
    print("   - Core switch: Cisco 9300 stack")
    print("   - Access switches: Redundant uplinks")
    print("   - VLANs: Segmented by tier")
    print("   - Routing: HSRP/VRRP")

    print("\n11. SECURITY:")
    print("   - Secrets: HashiCorp Vault cluster")
    print("   - SSL/TLS: Let's Encrypt với auto-renewal")
    print("   - WAF: ModSecurity trên Nginx")
    print("   - IDS/IPS: Suricata")

    print("\n12. BACKUP & DISASTER RECOVERY:")
    print("   - RTO (Recovery Time Objective): < 1 giờ")
    print("   - RPO (Recovery Point Objective): < 15 phút")
    print("   - Backup frequency: Continuous + Daily")
    print("   - DR site: Hot standby")

    print("\n" + "="*60)
    print("Tất cả các thành phần đều được cấu hình HA để đảm bảo")
    print("uptime 99.9% và khả năng chịu lỗi cao!")
    print("="*60)
