from diagrams import Cluster, Diagram
from diagrams.onprem.client import Users
from diagrams.programming.language import Java
from diagrams.onprem.queue import <PERSON>f<PERSON>
from diagrams.onprem.inmemory import Redis
from diagrams.saas.chat import Slack
from diagrams.generic.database import SQL
from diagrams.onprem.network import Nginx
from diagrams.onprem.container import Docker
from diagrams.onprem.analytics import Tableau
from diagrams.onprem.monitoring import Prometheus
from diagrams.custom import Custom
from diagrams.onprem.compute import Server

with Diagram("Triển khai hệ thống CDP đa kênh VNPT", show=False):

    users = Users("Admin / Agent / Retail / CC")

    with Cluster("Frontend - VueJS"):
        portal_ui = [
            Custom("Admin UI", "./icons/vue.png"),
            Custom("Agent UI", "./icons/vue.png"),
            Custom("Retail UI", "./icons/vue.png"),
            Custom("CC UI", "./icons/vue.png")
        ]

    api_gateway = Nginx("API Gateway")

    with Cluster("Backend Services - Java SpringBoot"):

        with Cluster("Channels Layer"):
            sms = Java("SMS Service")
            mms = Java("MMS Service")
            zns = Java("ZNS Service")
            email = Java("Email Service")
            push = Java("Push Noti Service")
            vnpt_msg = Java("VNPT Msg Service")
            others = Java("Viber/Telegram")

        with Cluster("Business Services"):
            policy = Java("Policy Mgmt")
            brandname = Java("Brandname Mgmt")
            template = Java("Template Mgmt")
            contract = Java("Contract Mgmt")
            pricing = Java("Pricing Plan")
            lba = Java("LBA Targeting")
            report = Java("Reporting")
            user_mgmt = Java("User Mgmt")

        with Cluster("Multi-channel Campaign"):
            campaign = Java("Campaign Mgmt")
            segment = Java("Segmentation")
            automation = Java("Automation")
            event = Java("Event Trigger")
            followup = Java("Follow-up")
            analytics = Java("Performance Analytics")

        with Cluster("Customer Data Platform"):
            datacollect = Java("Data Collection")
            insight = Java("Customer Insight")
            crm = Java("CRM Integration")
            customer360 = Java("Customer 360")
            datastudio = Tableau("Data Studio")
            ai = Java("AI Prediction")

    kafka = Kafka("Kafka Bus")
    redis = Redis("Redis Cache")
    db = SQL("Customer DB")

    with Cluster("External Integrations"):
        sms_non_vnpt = Server("SMS Brandname Non-VNPT")
        ott = Slack("OTT: Zalo, Viber, Telegram")

    with Cluster("VNPT Infrastructure"):
        smpp = Server("SMPP GW")
        mmsc = Server("MMSC GW")
        bigdata = Server("Big Data GW")
        mnp = Server("MNP Service")
        iba = Server("IBA")
        vnpt_1591 = Server("1591 Service")

    # Connections
    users >> portal_ui >> api_gateway
    api_gateway >> kafka
    kafka >> sms >> smpp
    kafka >> mms >> mmsc
    kafka >> vnpt_msg
    kafka >> email
    kafka >> zns
    kafka >> others >> ott
    kafka >> push

    api_gateway >> brandname >> sms_non_vnpt
    api_gateway >> contract
    api_gateway >> user_mgmt
    api_gateway >> insight >> datastudio
    api_gateway >> customer360
    api_gateway >> ai
    api_gateway >> policy
    api_gateway >> template
    api_gateway >> campaign
    api_gateway >> automation
    api_gateway >> event
    api_gateway >> followup
    api_gateway >> analytics
    api_gateway >> datacollect
    api_gateway >> crm

    # Shared storage
    [sms, mms, zns, email, vnpt_msg, automation, campaign, insight] >> redis
    [user_mgmt, crm, customer360, datacollect] >> db
