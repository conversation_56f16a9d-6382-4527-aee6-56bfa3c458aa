#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> đồ HLD (High Level Design) cho hệ thống Wifi Marketing ConnectX
Sử dụng thư viện diagrams để tạo sơ đồ kiến trúc hệ thống
"""

from diagrams import Diagram, Cluster, Edge
from diagrams.onprem.client import Users, Client
from diagrams.onprem.network import Internet, Nginx
from diagrams.onprem.compute import Server
from diagrams.onprem.database import MySQL
from diagrams.onprem.inmemory import Redis
from diagrams.programming.framework import Vue
from diagrams.programming.language import PHP
from diagrams.onprem.monitoring import Grafana
from diagrams.generic.device import Mobile, Tablet
from diagrams.generic.network import Router
from diagrams.custom import Custom

def create_connectx_hld():
    """Tạo sơ đồ HLD cho hệ thống ConnectX"""
    
    with Diagram("Hệ thống Wifi Marketing ConnectX - HLD",
                 filename="connectx_hld",
                 show=False,
                 direction="TB",
                 graph_attr={
                     "fontsize": "16",
                     "fontname": "Arial",
                     "bgcolor": "white",
                     "pad": "0.5",
                     "nodesep": "0.6",
                     "ranksep": "1.2",
                     "splines": "ortho"
                 }):
        
        # Người dùng cuối
        with Cluster("Người dùng"):
            end_users = [
                Mobile("Điện thoại"),
                Tablet("Máy tính bảng"),
                Client("Laptop")
            ]
        
        # Quản trị viên
        with Cluster("Quản trị viên"):
            admin_users = [
                Client("Admin"),
                Client("Đại lý"),
                Client("Khách hàng")
            ]
        
        # Tầng mạng
        with Cluster("Tầng mạng"):
            wifi_router = Router("WiFi Router")
            radius_server = Server("RADIUS Server")
            internet = Internet("Internet")
        
        # External Apps
        with Cluster("Ứng dụng ngoài"):
            zalo_miniapp = Custom("Zalo Mini App", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/zalo.png")

        # Load Balancer (bao gồm cả Web Server)
        load_balancer = Nginx("Nginx Load Balancer\n& Web Server")
        
        # Tầng ứng dụng
        with Cluster("Tầng ứng dụng"):

            # CMS Frontend
            cms_app = Vue("CMS Admin Panel")

            # Captive Portal
            portal_app = Vue("Captive Portal")
        
        # Tầng API
        with Cluster("Backend API"):
            api_server = PHP("API Server")

            with Cluster("API Modules"):
                # Hàng 1: Auth & User Management
                auth_module = Custom("Auth Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
                user_module = Custom("User Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
                role_module = Custom("Role Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
                agent_module = Custom("Agent Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")

                # Tạo kết nối ẩn để buộc layout hàng 1
                auth_module - user_module - role_module - agent_module

                # Hàng 2: Device & Campaign
                device_module = Custom("Device Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
                campaign_module = Custom("Campaign Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
                content_module = Custom("Content Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
                portal_module = Custom("Portal Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")

                # Tạo kết nối ẩn để buộc layout hàng 2
                device_module - campaign_module - content_module - portal_module

                # Hàng 3: Customer & Business
                customer_module = Custom("Customer Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
                target_module = Custom("Target Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
                package_module = Custom("Package Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
                payment_module = Custom("Payment Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")

                # Tạo kết nối ẩn để buộc layout hàng 3
                customer_module - target_module - package_module - payment_module

                # Hàng 4: Analytics & Reporting
                analytics_module = Custom("Analytics Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
                report_module = Custom("Report Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
                dashboard_module = Custom("Dashboard Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")

                # Tạo kết nối ẩn để buộc layout hàng 4
                analytics_module - report_module - dashboard_module

                # Tạo kết nối dọc để buộc các hàng xuống dòng
                auth_module >> Edge(style="invis") >> device_module
                user_module >> Edge(style="invis") >> campaign_module
                role_module >> Edge(style="invis") >> content_module
                agent_module >> Edge(style="invis") >> portal_module

                device_module >> Edge(style="invis") >> customer_module
                campaign_module >> Edge(style="invis") >> target_module
                content_module >> Edge(style="invis") >> package_module
                portal_module >> Edge(style="invis") >> payment_module

                customer_module >> Edge(style="invis") >> analytics_module
                target_module >> Edge(style="invis") >> report_module
                package_module >> Edge(style="invis") >> dashboard_module
        
        # Tầng Cache
        with Cluster("Cache Layer"):
            redis_cache = Redis("Redis Cache")
            redis_session = Redis("Redis Session")
        
        # Tầng dữ liệu
        with Cluster("Tầng dữ liệu"):
            mysql_db = MySQL("MySQL Database\n(Monolithic)")
        
        # Monitoring
        with Cluster("Giám sát"):
            monitoring = Grafana("Monitoring & Logs")
        
        # Kết nối các thành phần

        # Người dùng cuối -> WiFi -> RADIUS -> Internet
        for user in end_users:
            user >> Edge(label="WiFi", style="dashed") >> wifi_router

        wifi_router >> Edge(label="Auth", color="red") >> radius_server
        wifi_router >> internet >> load_balancer

        # RADIUS Server connections
        radius_server >> Edge(label="Auth Query", color="red") >> api_server
        radius_server >> Edge(label="User Data", color="red") >> mysql_db

        # Admin users -> Internet -> Load Balancer
        for admin in admin_users:
            admin >> Edge(label="HTTPS", color="blue") >> internet

        # Zalo Mini App -> Captive Portal
        zalo_miniapp >> Edge(label="API", color="green") >> portal_app

        internet >> load_balancer

        # Load Balancer -> Applications
        load_balancer >> Edge(label="Route & Serve", color="green") >> cms_app
        load_balancer >> Edge(label="Route & Serve", color="green") >> portal_app

        # Frontend -> Backend API
        cms_app >> Edge(label="REST API", color="orange") >> api_server
        portal_app >> Edge(label="REST API", color="orange") >> api_server

        # API Server -> Cache
        api_server >> Edge(label="Cache", color="red") >> redis_cache
        api_server >> Edge(label="Session", color="red") >> redis_session

        # API Server -> Database
        api_server >> Edge(label="SQL", color="purple") >> mysql_db

        # Monitoring connections
        api_server >> Edge(label="Logs", style="dotted") >> monitoring
        mysql_db >> Edge(label="Metrics", style="dotted") >> monitoring
        redis_cache >> Edge(label="Metrics", style="dotted") >> monitoring

def create_deployment_architecture():
    """Tạo sơ đồ kiến trúc triển khai"""

    with Diagram("ConnectX - Kiến trúc triển khai",
                 filename="connectx_deployment",
                 show=False,
                 direction="TB",
                 graph_attr={
                     "fontsize": "14",
                     "fontname": "Arial",
                     "bgcolor": "white",
                     "pad": "0.5"
                 }):

        # Production Environment
        with Cluster("Production Environment"):

            # Load Balancer Layer
            with Cluster("Load Balancer Layer"):
                lb_primary = Nginx("Nginx LB Primary")
                lb_backup = Nginx("Nginx LB Backup")

            # Web Server Layer
            with Cluster("Web Server Layer"):
                web1 = Nginx("Web Server 1")
                web2 = Nginx("Web Server 2")

            # Application Layer
            with Cluster("Application Layer"):
                app1 = PHP("PHP-FPM 1")
                app2 = PHP("PHP-FPM 2")

                # RADIUS Servers
                radius1 = Server("RADIUS 1")
                radius2 = Server("RADIUS 2")

            # Cache Layer
            with Cluster("Cache Layer"):
                redis_master = Redis("Redis Master")
                redis_slave = Redis("Redis Slave")

            # Database Layer
            with Cluster("Database Layer"):
                mysql_master = MySQL("MySQL Master")
                mysql_slave = MySQL("MySQL Slave")

        # External Services
        with Cluster("External Services"):
            zalo_service = Custom("Zalo Platform", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/Logo-Zalo.webp")
            monitoring_service = Grafana("Monitoring")

        # Connections
        lb_primary >> Edge(label="Active", color="green") >> web1
        lb_primary >> Edge(label="Active", color="green") >> web2
        lb_backup >> Edge(label="Standby", style="dashed", color="gray") >> web1
        lb_backup >> Edge(label="Standby", style="dashed", color="gray") >> web2

        web1 >> Edge(label="FastCGI", color="blue") >> app1
        web2 >> Edge(label="FastCGI", color="blue") >> app2

        app1 >> Edge(label="Cache", color="red") >> redis_master
        app2 >> Edge(label="Cache", color="red") >> redis_master
        redis_master >> Edge(label="Replication", color="orange") >> redis_slave

        app1 >> Edge(label="Write", color="purple") >> mysql_master
        app2 >> Edge(label="Write", color="purple") >> mysql_master
        app1 >> Edge(label="Read", color="green") >> mysql_slave
        app2 >> Edge(label="Read", color="green") >> mysql_slave
        mysql_master >> Edge(label="Replication", color="orange") >> mysql_slave

        radius1 >> Edge(label="Auth", color="red") >> app1
        radius2 >> Edge(label="Auth", color="red") >> app2
        radius1 >> Edge(label="User Data", color="red") >> mysql_master
        radius2 >> Edge(label="User Data", color="red") >> mysql_master

        app1 >> Edge(label="API", color="cyan") >> zalo_service
        app2 >> Edge(label="API", color="cyan") >> zalo_service

        app1 >> Edge(label="Metrics", style="dotted") >> monitoring_service
        app2 >> Edge(label="Metrics", style="dotted") >> monitoring_service
        mysql_master >> Edge(label="Metrics", style="dotted") >> monitoring_service
        redis_master >> Edge(label="Metrics", style="dotted") >> monitoring_service

if __name__ == "__main__":
    print("Đang tạo sơ đồ HLD cho hệ thống ConnectX...")

    # Tạo sơ đồ kiến trúc tổng thể
    create_connectx_hld()
    print("✓ Đã tạo sơ đồ kiến trúc tổng thể: connectx_hld.png")

    # Tạo sơ đồ kiến trúc triển khai
    create_deployment_architecture()
    print("✓ Đã tạo sơ đồ kiến trúc triển khai: connectx_deployment.png")

    print("\nHướng dẫn sử dụng:")
    print("1. Cài đặt thư viện: pip install diagrams")
    print("2. Chạy script: python connectx_hld_diagram.py")
    print("3. Xem kết quả trong các file PNG được tạo:")
    print("   - connectx_hld.png: Kiến trúc tổng thể")
    print("   - connectx_features.png: Chi tiết tính năng")
    print("   - connectx_dataflow.png: Luồng dữ liệu")
    print("   - connectx_deployment.png: Kiến trúc triển khai")
