# ConnectX - Tóm tắt Kiến trúc Nginx HA

## 🏗️ Tổng quan Kiến trúc

Hệ thống ConnectX được thiết kế với kiến trúc **Nginx High Availability** và **All-in-One Application Servers**, tối ưu hóa cho việc triển khai đơn giản nhưng vẫn đảm bảo tính sẵn sàng cao.

## 📊 Sơ đồ Kiến trúc

```
Internet/CDN
     ↓
pfSense Firewall
     ↓
Nginx HA Proxy Layer (Keepalived VIP)
├── Nginx Proxy Master (*********)
└── Nginx Proxy Backup (*********)
     ↓
All-in-One Application Servers
├── Server 1 (*********)
│   ├── Nginx Web Server
│   ├── Vue.js CMS + Portal
│   ├── PHP-FPM (Yii Framework)
│   └── FreeRADIUS
├── Server 2 (*********)
│   └── [Same components]
└── Server 3 (*********)
    └── [Same components]
     ↓
Database Layer (HA)
├── MySQL Cluster (Master + 2 Slaves)
├── Redis Cluster (Master + Slaves + Sentinel)
└── Shared Storage (Ceph)
```

## 🎯 Đặc điểm Chính

### ✅ **Ưu điểm**
- **Đơn giản triển khai**: Tất cả components trên cùng server
- **Dễ quản lý**: Ít moving parts, cấu hình tập trung
- **High Availability**: 99.9% uptime với auto-failover
- **Scalable**: Dễ dàng thêm/bớt application servers
- **Cost-effective**: Tối ưu tài nguyên server

### ⚠️ **Cân nhắc**
- **Single point of failure**: Nếu 1 server down, mất 1/3 capacity
- **Resource sharing**: CPU/Memory được chia sẻ giữa các services
- **Complexity**: Troubleshooting phức tạp hơn khi có vấn đề

## 🔧 Thành phần Hệ thống

### **1. Nginx HA Proxy Layer**
```
Component: Nginx + Keepalived
Purpose: Load balancing và high availability
Configuration: Active-Backup với VIP
Failover Time: < 5 giây
```

### **2. All-in-One Application Servers**
```
Quantity: 3 servers
Components per server:
├── Nginx Web Server (Port 80/443)
├── Vue.js Frontend (CMS + Portal)
├── PHP-FPM Backend (Yii Framework)
└── FreeRADIUS (Port 1812/1813)

Load Balancing: Round-robin với health check
Session Persistence: Sticky sessions
```

### **3. Database Layer**
```
MySQL Cluster:
├── Master: ********* (Read/Write)
├── Slave 1: ********* (Read-only)
└── Slave 2: ********* (Read-only)

Redis Cluster:
├── Master: *********
├── Slave 1: *********
├── Slave 2: *********
└── Sentinel: ********* (Monitoring)
```

## 📈 Performance Metrics

### **Capacity Planning**
```
Per Application Server:
├── CPU: 8 cores (recommended)
├── RAM: 16GB (recommended)
├── Storage: 500GB SSD
└── Network: 1Gbps

Total Cluster Capacity:
├── Concurrent Users: ~10,000
├── API Requests: ~1,000 RPS
├── Database Connections: ~1,500
└── RADIUS Auth: ~500 RPS
```

### **High Availability Metrics**
```
Uptime Target: 99.9% (8.76 hours downtime/year)
RTO (Recovery Time): < 1 hour
RPO (Recovery Point): < 15 minutes
Failover Time: 3-10 seconds
Health Check Interval: 30 seconds
```

## 🛡️ Security Features

### **Network Security**
- pfSense Firewall với rule-based filtering
- SSL/TLS termination tại Nginx HA Proxy
- Rate limiting cho API endpoints
- CORS protection

### **Application Security**
- WAF (Web Application Firewall) rules
- Input validation và sanitization
- SQL injection protection
- XSS protection headers

### **Data Security**
- Database encryption at rest
- Redis password authentication
- HashiCorp Vault cho secret management
- Regular security updates

## 🔄 Deployment Strategy

### **Blue-Green Deployment**
```
1. Deploy to staging environment
2. Run automated tests
3. Switch traffic gradually (10% → 50% → 100%)
4. Monitor metrics và rollback if needed
```

### **Rolling Updates**
```
1. Update Server 1 → Health check → OK
2. Update Server 2 → Health check → OK  
3. Update Server 3 → Health check → OK
4. Verify all services running normally
```

## 📊 Monitoring Stack

### **Metrics Collection**
- **Prometheus**: System và application metrics
- **Grafana**: Visualization và dashboards
- **AlertManager**: Alerting và notifications

### **Log Management**
- **ELK Stack**: Centralized logging
- **Log rotation**: Automated cleanup
- **Log analysis**: Error tracking và performance

### **Health Checks**
```
Application Level:
├── /health endpoint (HTTP 200)
├── Database connectivity
├── Redis connectivity
└── RADIUS service status

Infrastructure Level:
├── CPU usage < 80%
├── Memory usage < 85%
├── Disk usage < 90%
└── Network connectivity
```

## 🚀 Scaling Strategy

### **Horizontal Scaling**
```
Current: 3 Application Servers
Scale to: 5-7 Application Servers
Process:
1. Add new server to upstream
2. Deploy application code
3. Update Nginx configuration
4. Reload Nginx (zero downtime)
```

### **Vertical Scaling**
```
CPU: 8 → 16 cores
RAM: 16GB → 32GB
Storage: 500GB → 1TB SSD
Network: 1Gbps → 10Gbps
```

## 💾 Backup & Recovery

### **Backup Strategy**
```
MySQL:
├── Full backup: Daily at 2:00 AM
├── Incremental: Every 6 hours
└── Retention: 30 days

Application Files:
├── Code repository: Git với tags
├── Static files: Daily rsync
└── Configuration: Ansible playbooks

Redis:
├── RDB snapshots: Every 15 minutes
├── AOF logs: Real-time
└── Retention: 7 days
```

### **Disaster Recovery**
```
RTO: < 1 hour
RPO: < 15 minutes
DR Site: Hot standby
Failover: Manual trigger
Recovery Testing: Monthly
```

## 📋 Operational Procedures

### **Daily Operations**
- [ ] Check system health dashboards
- [ ] Review error logs
- [ ] Monitor resource usage
- [ ] Verify backup completion

### **Weekly Operations**
- [ ] Security updates
- [ ] Performance review
- [ ] Capacity planning review
- [ ] DR testing

### **Monthly Operations**
- [ ] Full system backup test
- [ ] Security audit
- [ ] Performance optimization
- [ ] Documentation updates

## 🎯 Success Criteria

### **Performance KPIs**
- ✅ Response time < 200ms (95th percentile)
- ✅ Uptime > 99.9%
- ✅ Error rate < 0.1%
- ✅ Throughput > 1,000 RPS

### **Operational KPIs**
- ✅ Deployment time < 30 minutes
- ✅ Recovery time < 1 hour
- ✅ Mean time to detection < 5 minutes
- ✅ Mean time to resolution < 30 minutes

---

## 📞 Support & Maintenance

### **24/7 Monitoring**
- Automated alerting
- On-call rotation
- Escalation procedures
- Incident response

### **Regular Maintenance**
- Security patches: Monthly
- System updates: Quarterly
- Hardware refresh: 3-5 years
- Architecture review: Annually

---

**Kiến trúc này cung cấp sự cân bằng tối ưu giữa đơn giản, hiệu suất và độ tin cậy cho hệ thống ConnectX WiFi Marketing.**
