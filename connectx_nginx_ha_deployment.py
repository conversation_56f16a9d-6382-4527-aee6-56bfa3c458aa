#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> hình triển khai vật lý ConnectX với Nginx HA Proxy
Frontend, Backend, Web Server triển khai chung trên cùng server với HA nodes
"""

from diagrams import Diagram, Cluster, Edge
from diagrams.onprem.client import Users, Client
from diagrams.onprem.network import Internet, Nginx
from diagrams.onprem.compute import Server
from diagrams.onprem.database import MySQL
from diagrams.onprem.inmemory import Redis
from diagrams.programming.framework import Vue
from diagrams.programming.language import PHP
from diagrams.onprem.monitoring import <PERSON><PERSON>, Prometheus
from diagrams.generic.device import Mobile, Tablet
from diagrams.generic.network import Router, Switch, Firewall
from diagrams.custom import Custom
from diagrams.onprem.storage import Ceph
from diagrams.onprem.security import Vault
from diagrams.onprem.network import Pfsense

def create_nginx_ha_deployment():
    """Tạo mô hình triển khai với Nginx HA và All-in-One servers"""
    
    with Diagram("ConnectX - Nginx HA với All-in-One Servers",
                 filename="connectx_nginx_ha_deployment",
                 show=False,
                 direction="TB",
                 graph_attr={
                     "fontsize": "14",
                     "fontname": "Arial",
                     "bgcolor": "white",
                     "pad": "0.8",
                     "nodesep": "0.8",
                     "ranksep": "1.5",
                     "splines": "ortho"
                 }):

        # Internet và CDN
        with Cluster("Internet & External"):
            internet = Internet("Internet")
            cdn = Custom("CloudFlare CDN", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/cloudflare.png")

        # Nginx HA Proxy Layer
        with Cluster("Nginx HA Proxy Layer"):
            # Virtual IP cho HA
            vip = Custom("Virtual IP\n(**********)\nKeepalived", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/keepalived.png")
            
            # Nginx HA Proxy nodes
            nginx_proxy1 = Nginx("Nginx HA Proxy-1\n(*********)\nMaster")
            nginx_proxy2 = Nginx("Nginx HA Proxy-2\n(*********)\nBackup")
            
            # HA connection
            nginx_proxy1 >> Edge(label="VRRP Heartbeat", color="red", style="dashed") >> nginx_proxy2

        # All-in-One Application Servers với HA
        with Cluster("All-in-One Application Servers"):
            
            # Server Node 1
            with Cluster("Server Node 1 (*********)"):
                server1_nginx = Nginx("Nginx Web Server")
                server1_vue_cms = Vue("CMS Admin Panel")
                server1_vue_portal = Vue("Captive Portal")
                server1_php = PHP("PHP-FPM Backend")
                server1_radius = Server("FreeRADIUS")
                
                # Internal connections trong server 1
                server1_nginx >> server1_vue_cms
                server1_nginx >> server1_vue_portal
                server1_nginx >> server1_php
                server1_php >> server1_radius

            # Server Node 2 (HA)
            with Cluster("Server Node 2 (*********)"):
                server2_nginx = Nginx("Nginx Web Server")
                server2_vue_cms = Vue("CMS Admin Panel")
                server2_vue_portal = Vue("Captive Portal")
                server2_php = PHP("PHP-FPM Backend")
                server2_radius = Server("FreeRADIUS")
                
                # Internal connections trong server 2
                server2_nginx >> server2_vue_cms
                server2_nginx >> server2_vue_portal
                server2_nginx >> server2_php
                server2_php >> server2_radius

            # Server Node 3 (HA)
            with Cluster("Server Node 3 (*********)"):
                server3_nginx = Nginx("Nginx Web Server")
                server3_vue_cms = Vue("CMS Admin Panel")
                server3_vue_portal = Vue("Captive Portal")
                server3_php = PHP("PHP-FPM Backend")
                server3_radius = Server("FreeRADIUS")
                
                # Internal connections trong server 3
                server3_nginx >> server3_vue_cms
                server3_nginx >> server3_vue_portal
                server3_nginx >> server3_php
                server3_php >> server3_radius

        # API Modules (Yii Framework) - Shared across all servers
        with Cluster("API Modules (Yii Framework)"):
            # Hàng 1: Auth & User Management
            auth_module = Custom("Auth Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
            user_module = Custom("User Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
            role_module = Custom("Role Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
            agent_module = Custom("Agent Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")

            # Hàng 2: Device & Campaign
            device_module = Custom("Device Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
            campaign_module = Custom("Campaign Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
            content_module = Custom("Content Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")
            portal_module = Custom("Portal Module", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/yii.svg")

            # Layout connections
            auth_module - user_module - role_module - agent_module
            device_module - campaign_module - content_module - portal_module
            auth_module >> Edge(style="invis") >> device_module

        # Database Layer với HA
        with Cluster("Database Layer (HA)"):
            # MySQL Cluster
            with Cluster("MySQL Cluster"):
                mysql_master = MySQL("MySQL Master\n(*********)")
                mysql_slave1 = MySQL("MySQL Slave-1\n(*********)")
                mysql_slave2 = MySQL("MySQL Slave-2\n(*********)")
                mysql_vip = Custom("MySQL VIP\n(*********0)", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/mysql.png")

            # Redis Cluster
            with Cluster("Redis Cluster"):
                redis_master = Redis("Redis Master\n(*********)")
                redis_slave1 = Redis("Redis Slave-1\n(*********)")
                redis_slave2 = Redis("Redis Slave-2\n(*********)")
                redis_sentinel = Custom("Redis Sentinel\n(*********)", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/redis.png")

        # Shared Storage
        with Cluster("Shared Storage"):
            storage_primary = Ceph("Ceph Primary")
            storage_backup = Ceph("Ceph Backup")

        # Monitoring & Management
        with Cluster("Monitoring & Management"):
            prometheus = Prometheus("Prometheus\n(*********)")
            grafana = Grafana("Grafana\n(*********)")
            backup_server = Server("Backup Server\n(*********)")
            vault_server = Vault("Vault\n(*********)")

        # External Services
        with Cluster("External Services"):
            zalo_service = Custom("Zalo Mini App", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/zalo.png")
            sms_gateway = Custom("SMS Gateway", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/sms.png")
            payment_gateway = Custom("Payment Gateway", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/payment.png")

        # Network Infrastructure
        with Cluster("Network Infrastructure"):
            firewall = Pfsense("pfSense Firewall")
            core_switch = Switch("Core Switch")

        # === CONNECTIONS ===

        # Internet flow
        internet >> cdn >> firewall >> vip

        # HA Proxy load balancing
        vip >> Edge(label="Active", color="green") >> nginx_proxy1
        vip >> Edge(label="Backup", color="orange", style="dashed") >> nginx_proxy2

        # Load balancing to application servers
        nginx_proxy1 >> Edge(label="HTTP/HTTPS", color="blue") >> server1_nginx
        nginx_proxy1 >> Edge(label="HTTP/HTTPS", color="blue") >> server2_nginx
        nginx_proxy1 >> Edge(label="HTTP/HTTPS", color="blue") >> server3_nginx

        nginx_proxy2 >> Edge(label="HTTP/HTTPS", color="blue", style="dashed") >> server1_nginx
        nginx_proxy2 >> Edge(label="HTTP/HTTPS", color="blue", style="dashed") >> server2_nginx
        nginx_proxy2 >> Edge(label="HTTP/HTTPS", color="blue", style="dashed") >> server3_nginx

        # Database connections
        mysql_master >> Edge(label="Replication", color="orange") >> mysql_slave1
        mysql_master >> Edge(label="Replication", color="orange") >> mysql_slave2
        mysql_vip >> mysql_master

        # Redis connections
        redis_master >> Edge(label="Replication", color="red") >> redis_slave1
        redis_master >> Edge(label="Replication", color="red") >> redis_slave2
        redis_sentinel >> Edge(label="Monitor", style="dotted") >> redis_master

        # Application to Database
        server1_php >> Edge(label="SQL", color="purple") >> mysql_vip
        server2_php >> Edge(label="SQL", color="purple") >> mysql_vip
        server3_php >> Edge(label="SQL", color="purple") >> mysql_vip

        # Application to Cache
        server1_php >> Edge(label="Cache", color="red") >> redis_master
        server2_php >> Edge(label="Cache", color="red") >> redis_master
        server3_php >> Edge(label="Cache", color="red") >> redis_master

        # RADIUS connections
        server1_radius >> Edge(label="Auth Query", color="green") >> mysql_vip
        server2_radius >> Edge(label="Auth Query", color="green") >> mysql_vip
        server3_radius >> Edge(label="Auth Query", color="green") >> mysql_vip

        # External services
        server1_php >> Edge(label="API", color="cyan") >> zalo_service
        server2_php >> Edge(label="API", color="cyan") >> sms_gateway
        server3_php >> Edge(label="API", color="cyan") >> payment_gateway

        # Storage connections
        storage_primary >> Edge(label="Backup", style="dashed") >> storage_backup
        mysql_master >> Edge(label="Data", color="gray") >> storage_primary

        # Monitoring
        prometheus >> Edge(label="Metrics", style="dotted") >> server1_nginx
        prometheus >> Edge(label="Metrics", style="dotted") >> server2_nginx
        prometheus >> Edge(label="Metrics", style="dotted") >> server3_nginx
        prometheus >> Edge(label="Metrics", style="dotted") >> mysql_master
        prometheus >> Edge(label="Metrics", style="dotted") >> redis_master

        grafana >> Edge(label="Query", color="cyan") >> prometheus

        # Backup
        backup_server >> Edge(label="Backup", style="dashed") >> mysql_master
        backup_server >> Edge(label="Backup", style="dashed") >> storage_primary

        # Security
        vault_server >> Edge(label="Secrets", color="red") >> server1_php
        vault_server >> Edge(label="Secrets", color="red") >> server2_php
        vault_server >> Edge(label="Secrets", color="red") >> server3_php

def create_nginx_config_details():
    """Tạo sơ đồ chi tiết cấu hình Nginx"""
    
    with Diagram("ConnectX - Chi tiết cấu hình Nginx HA",
                 filename="connectx_nginx_config",
                 show=False,
                 direction="LR",
                 graph_attr={
                     "fontsize": "12",
                     "fontname": "Arial",
                     "bgcolor": "white",
                     "pad": "0.5"
                 }):

        # Nginx HA Proxy Configuration
        with Cluster("Nginx HA Proxy Config"):
            proxy_master = Nginx("Nginx Proxy Master\nPriority: 100\nVIP: **********")
            proxy_backup = Nginx("Nginx Proxy Backup\nPriority: 90\nVIP: **********")
            
            proxy_master >> Edge(label="Keepalived VRRP\nCheck: nginx process", color="red") >> proxy_backup

        # Upstream Configuration
        with Cluster("Upstream Backend Servers"):
            upstream_config = Custom("Upstream Config\nbackend_servers", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/nginx.png")
            
            server1 = Server("Server-1\n*********:80\nweight=1")
            server2 = Server("Server-2\n*********:80\nweight=1")
            server3 = Server("Server-3\n*********:80\nweight=1")
            
            upstream_config >> server1
            upstream_config >> server2
            upstream_config >> server3

        # Load Balancing Methods
        with Cluster("Load Balancing Config"):
            lb_method = Custom("Load Balancing\n- Round Robin\n- Health Check\n- Session Persistence", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/nginx.png")
            
            health_check = Custom("Health Check\n/health endpoint\nInterval: 30s\nTimeout: 5s", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/health.png")

        # SSL/TLS Configuration
        with Cluster("SSL/TLS Config"):
            ssl_config = Custom("SSL Configuration\n- Let's Encrypt\n- TLS 1.2/1.3\n- HSTS", "/Users/<USER>/Documents/GitHub/diagrams/assets/img/ssl.png")

        # Connections
        proxy_master >> upstream_config
        proxy_backup >> Edge(style="dashed") >> upstream_config
        upstream_config >> lb_method
        lb_method >> health_check
        proxy_master >> ssl_config

if __name__ == "__main__":
    print("Đang tạo mô hình triển khai Nginx HA cho hệ thống ConnectX...")
    
    # Tạo mô hình triển khai chính
    create_nginx_ha_deployment()
    print("✓ Đã tạo mô hình Nginx HA deployment: connectx_nginx_ha_deployment.png")
    
    # Tạo chi tiết cấu hình Nginx
    create_nginx_config_details()
    print("✓ Đã tạo chi tiết cấu hình Nginx: connectx_nginx_config.png")
    
    print("\n" + "="*60)
    print("MÔ HÌNH NGINX HA VỚI ALL-IN-ONE SERVERS")
    print("="*60)
    
    print("\n1. NGINX HA PROXY LAYER:")
    print("   - Nginx Proxy Master: ********* (Priority: 100)")
    print("   - Nginx Proxy Backup: ********* (Priority: 90)")
    print("   - Virtual IP: ********** (Keepalived)")
    print("   - Health Check: nginx process monitoring")
    print("   - Failover time: < 5 giây")
    
    print("\n2. ALL-IN-ONE APPLICATION SERVERS:")
    print("   - Server 1: ********* (Nginx + Vue + PHP + RADIUS)")
    print("   - Server 2: ********* (Nginx + Vue + PHP + RADIUS)")
    print("   - Server 3: ********* (Nginx + Vue + PHP + RADIUS)")
    print("   - Load balancing: Round-robin với health check")
    print("   - Session persistence: Sticky sessions")
    
    print("\n3. THÀNH PHẦN TRÊN MỖI SERVER:")
    print("   ├── Nginx Web Server (Port 80/443)")
    print("   ├── Vue.js CMS Admin Panel")
    print("   ├── Vue.js Captive Portal")
    print("   ├── PHP-FPM Backend (Yii Framework)")
    print("   └── FreeRADIUS Server")
    
    print("\n4. DATABASE LAYER (HA):")
    print("   - MySQL Master: *********")
    print("   - MySQL Slaves: *********, *********")
    print("   - MySQL VIP: *********0")
    print("   - Redis Master: *********")
    print("   - Redis Slaves: *********, *********")
    print("   - Redis Sentinel: *********")
    
    print("\n5. ƯU ĐIỂM CỦA MÔ HÌNH NÀY:")
    print("   ✓ Đơn giản triển khai và quản lý")
    print("   ✓ Giảm độ phức tạp network")
    print("   ✓ Tất cả service trên cùng server")
    print("   ✓ Nginx làm cả proxy và web server")
    print("   ✓ High Availability với 3 nodes")
    print("   ✓ Auto failover và load balancing")
    
    print("\n6. CẤU HÌNH NGINX UPSTREAM:")
    print("   upstream backend_servers {")
    print("       server *********:80 weight=1 max_fails=3 fail_timeout=30s;")
    print("       server *********:80 weight=1 max_fails=3 fail_timeout=30s;")
    print("       server *********:80 weight=1 max_fails=3 fail_timeout=30s;")
    print("   }")
    
    print("\n" + "="*60)
    print("Mô hình này tối ưu cho việc triển khai đơn giản")
    print("với đầy đủ tính năng HA và dễ dàng scale!")
    print("="*60)
